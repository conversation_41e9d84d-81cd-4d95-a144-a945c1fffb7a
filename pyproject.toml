[tool.poetry]
name = "ipsec-evaluator"
version = "0.1.0"
description = "Modern IPsec compliance testing tool for ANSSI IPsecDR corpus"
authors = ["IPsec Evaluator Team <<EMAIL>>"]
readme = "README.md"
license = "MIT"
packages = [{include = "ipsec_evaluator", from = "src"}]

[tool.poetry.dependencies]
python = "^3.12"
# Core dependencies
cryptography = "^43.0.1"
pydantic = "^2.9.2"
pyyaml = "^6.0.2"
rich = "^13.9.2"
scapy = "^2.6.0"
typer = "^0.12.5"
# Async support
aiofiles = "^24.1.0"
# Networking
netaddr = "^1.3.0"
# Logging
structlog = "^24.4.0"

[tool.poetry.group.dev]
optional = true

[tool.poetry.group.dev.dependencies]
# Testing
pytest = "^8.3.3"
pytest-asyncio = "^0.24.0"
pytest-cov = "^5.0.0"
pytest-mock = "^3.14.0"
# Code quality
black = "^24.10.0"
isort = "^5.13.2"
flake8 = "^7.1.1"

[tool.poetry.scripts]
ipsec-evaluator = "ipsec_evaluator.cli:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

# Tool configurations
[tool.black]
line-length = 88
target-version = ['py312']

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["ipsec_evaluator"]

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests", 
    "e2e: End-to-end tests",
    "slow: Slow running tests",
    "infra: Infrastructure tests requiring containers"
]
asyncio_mode = "auto"

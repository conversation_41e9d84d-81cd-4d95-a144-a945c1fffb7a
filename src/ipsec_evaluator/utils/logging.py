"""
Logging configuration and utilities with Rich integration.
"""

import logging
import sys
from pathlib import Path
from typing import Optional

import structlog
from rich.console import Console
from rich.logging import <PERSON><PERSON>andler
from rich.traceback import install as install_rich_traceback
from rich.panel import Panel
from rich.text import Text


# Install rich traceback handler
install_rich_traceback(show_locals=True)

# Global console for logging
_console = Console(stderr=True)


class RichStructlogHandler(RichHandler):
    """Custom Rich handler for structlog integration."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, console=_console, **kwargs)


def setup_logging(
    level: str = "INFO",
    log_file: Optional[Path] = None,
    verbose: bool = False,
    rich_tracebacks: bool = True
) -> None:
    """
    Setup Rich-enhanced structured logging for the application.
    
    Args:
        level: Logging level
        log_file: Optional log file path
        verbose: Enable verbose logging
        rich_tracebacks: Enable Rich traceback formatting
    """
    if verbose:
        level = "DEBUG"
    
    # Configure Rich logging
    rich_handler = RichStructlogHandler(
        level=getattr(logging, level.upper()),
        show_time=True,
        show_level=True,
        show_path=verbose,
        markup=True,
        rich_tracebacks=rich_tracebacks,
        tracebacks_show_locals=verbose,
    )
    
    # Configure structlog with Rich integration
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            _rich_processor if not log_file else structlog.processors.JSONRenderer(),
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard logging
    handlers = [rich_handler]
    
    # Add file handler if specified
    if log_file:
        log_file.parent.mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(
            logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
        )
        handlers.append(file_handler)
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        handlers=handlers,
        format="%(message)s",
    )
    
    # Suppress noisy loggers
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)


def _rich_processor(logger, method_name, event_dict):
    """Process log events for Rich display."""
    level = event_dict.get("level", "info").upper()
    
    # Color mapping for log levels
    level_colors = {
        "DEBUG": "dim blue",
        "INFO": "blue",
        "WARNING": "yellow",
        "ERROR": "red",
        "CRITICAL": "bold red",
    }
    
    # Format the message with Rich markup
    message = event_dict.get("event", "")
    logger_name = event_dict.get("logger", "")
    
    # Add context if available
    context_items = []
    for key, value in event_dict.items():
        if key not in ["event", "level", "logger", "timestamp"]:
            context_items.append(f"{key}={value}")
    
    if context_items:
        context_str = " ".join(context_items)
        message = f"{message} [{context_str}]"
    
    # Return formatted message
    return f"[{level_colors.get(level, 'white')}]{level}[/] {logger_name}: {message}"


def get_logger(name: str) -> structlog.BoundLogger:
    """Get a structured logger instance."""
    return structlog.get_logger(name)


def log_test_start(scenario_name: str, mode: str) -> None:
    """Log test start with Rich formatting."""
    panel = Panel(
        f"[bold blue]Starting Test[/bold blue]\n"
        f"Scenario: [green]{scenario_name}[/green]\n"
        f"Mode: [yellow]{mode}[/yellow]",
        border_style="blue",
        title="Test Execution"
    )
    _console.print(panel)


def log_test_complete(scenario_name: str, status: str, duration: float) -> None:
    """Log test completion with Rich formatting."""
    status_color = "green" if status == "completed" else "red"
    
    panel = Panel(
        f"[bold {status_color}]Test {status.title()}[/bold {status_color}]\n"
        f"Scenario: [green]{scenario_name}[/green]\n"
        f"Duration: [blue]{duration:.2f}s[/blue]",
        border_style=status_color,
        title="Test Result"
    )
    _console.print(panel)


def log_compliance_result(scenario_name: str, compliance_level: str, score: float) -> None:
    """Log compliance result with Rich formatting."""
    compliance_colors = {
        "compliant": "green",
        "non_compliant": "red", 
        "partial": "yellow",
        "unknown": "dim"
    }
    
    color = compliance_colors.get(compliance_level, "white")
    
    panel = Panel(
        f"[bold {color}]Compliance: {compliance_level.replace('_', ' ').title()}[/bold {color}]\n"
        f"Scenario: [green]{scenario_name}[/green]\n"
        f"Score: [blue]{score:.1f}%[/blue]",
        border_style=color,
        title="Compliance Assessment"
    )
    _console.print(panel)


def log_error_with_context(error: Exception, context: dict = None) -> None:
    """Log error with Rich formatting and context."""
    error_text = Text()
    error_text.append("Error: ", style="bold red")
    error_text.append(str(error), style="red")
    
    if context:
        error_text.append("\nContext:\n", style="dim")
        for key, value in context.items():
            error_text.append(f"  {key}: {value}\n", style="dim")
    
    panel = Panel(
        error_text,
        border_style="red",
        title="[bold red]Error[/bold red]"
    )
    _console.print(panel)


def create_progress_logger(name: str):
    """Create a logger for progress tracking."""
    return get_logger(f"progress.{name}")


def get_console() -> Console:
    """Get the global Rich console instance."""
    return _console

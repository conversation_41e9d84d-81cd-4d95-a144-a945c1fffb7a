"""
Main CLI application for IPsec Evaluator.
"""

import sys
from datetime import datetime
from pathlib import Path
from typing import List, Optional

import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.prompt import Confirm, Prompt
from rich.tree import Tree
from rich import print as rprint

from ..models.config import TestConfiguration
from ..utils.logging import setup_logging, get_logger

# Create the main CLI app
app = typer.Typer(
    name="ipsec-evaluator",
    help="Modern IPsec compliance testing tool for ANSSI IPsecDR corpus",
    add_completion=False,
)

# Create sub-commands
test_app = typer.Typer(help="Test execution commands")
results_app = typer.Typer(help="Results management commands")
scenario_app = typer.Typer(help="Scenario management commands")
config_app = typer.Typer(help="Configuration management commands")

app.add_typer(test_app, name="test")
app.add_typer(results_app, name="results")
app.add_typer(scenario_app, name="scenario")
app.add_typer(config_app, name="config")

console = Console()
logger = get_logger(__name__)


def print_banner():
    """Print the application banner."""
    banner = """
[bold blue]IPsec Evaluator[/bold blue] [dim]v0.1.0[/dim]
[italic]Modern IPsec compliance testing for ANSSI IPsecDR corpus[/italic]
"""
    console.print(Panel(banner, border_style="blue"))


@test_app.command("run")
def run_test(
    scenario: str = typer.Argument(..., help="Test scenario name to execute"),
    config_file: Optional[Path] = typer.Option(
        None,
        "--config",
        "-c", 
        help="Configuration file path"
    ),
    mode: str = typer.Option(
        "both",
        "--mode",
        "-m",
        help="Test mode: initiator, responder, or both"
    ),
    verbose: bool = typer.Option(
        False,
        "--verbose",
        "-v",
        help="Enable verbose output"
    ),
    output_dir: Optional[Path] = typer.Option(
        None,
        "--output",
        "-o",
        help="Output directory for results"
    ),
    dry_run: bool = typer.Option(
        False,
        "--dry-run",
        help="Show what would be executed without running tests"
    ),
) -> None:
    """Execute IPsec compliance tests."""
    
    print_banner()
    
    # Setup logging
    setup_logging(verbose=verbose)
    logger.info("Starting IPsec Evaluator test execution")
    
    # Load configuration
    config = _load_configuration(config_file)
    
    # Update config with CLI options
    config.global_config.verbose = verbose
    if output_dir:
        config.results_dir = output_dir
    
    # Display test plan
    scenarios = [scenario]  # Convert single scenario to list for compatibility
    _display_test_plan(scenarios, mode, config, dry_run)
    
    if dry_run:
        console.print("[yellow]Dry run completed - no tests executed[/yellow]")
        return
    
    # Confirm execution
    if not Confirm.ask("Execute this test?"):
        console.print("[yellow]Test execution cancelled[/yellow]")
        return
    
    try:
        # Execute tests with progress tracking
        _execute_tests_with_progress(scenarios, mode, config)
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Test execution interrupted by user[/yellow]")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]Error:[/red] {e}")
        logger.error(f"Test execution failed: {e}")
        raise typer.Exit(1)


@test_app.command("batch")
def run_batch_tests(
    scenarios_file: Path = typer.Argument(..., help="File containing list of scenarios to run"),
    config_file: Optional[Path] = typer.Option(
        None,
        "--config",
        "-c", 
        help="Configuration file path"
    ),
    mode: str = typer.Option(
        "both",
        "--mode",
        "-m",
        help="Test mode: initiator, responder, or both"
    ),
    verbose: bool = typer.Option(
        False,
        "--verbose",
        "-v",
        help="Enable verbose output"
    ),
    output_dir: Optional[Path] = typer.Option(
        None,
        "--output",
        "-o",
        help="Output directory for results"
    ),
) -> None:
    """Execute multiple IPsec compliance tests from a file."""
    
    print_banner()
    
    if not scenarios_file.exists():
        console.print(f"[red]Error:[/red] Scenarios file not found: {scenarios_file}")
        raise typer.Exit(1)
    
    # Read scenarios from file
    try:
        with open(scenarios_file, 'r') as f:
            scenarios = [line.strip() for line in f if line.strip() and not line.startswith('#')]
    except Exception as e:
        console.print(f"[red]Error reading scenarios file:[/red] {e}")
        raise typer.Exit(1)
    
    if not scenarios:
        console.print("[yellow]No scenarios found in file[/yellow]")
        return
    
    # Setup logging
    setup_logging(verbose=verbose)
    logger.info(f"Starting batch execution of {len(scenarios)} scenarios")
    
    # Load configuration
    config = _load_configuration(config_file)
    
    # Update config with CLI options
    config.global_config.verbose = verbose
    if output_dir:
        config.results_dir = output_dir
    
    # Display test plan
    _display_test_plan(scenarios, mode, config, False)
    
    # Confirm execution
    if not Confirm.ask(f"Execute {len(scenarios)} tests?"):
        console.print("[yellow]Batch execution cancelled[/yellow]")
        return
    
    try:
        # Execute tests with progress tracking
        _execute_tests_with_progress(scenarios, mode, config)
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Batch execution interrupted by user[/yellow]")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]Error:[/red] {e}")
        logger.error(f"Batch execution failed: {e}")
        raise typer.Exit(1)


@test_app.command("list")
def list_tests(
    scenarios_dir: Optional[Path] = typer.Option(
        None,
        "--dir",
        "-d",
        help="Scenarios directory path"
    ),
    filter_tag: Optional[str] = typer.Option(
        None,
        "--tag",
        "-t",
        help="Filter scenarios by tag"
    ),
) -> None:
    """List available test scenarios."""
    
    if scenarios_dir is None:
        scenarios_dir = Path("scenarios")
    
    console.print(f"[bold blue]Available Test Scenarios[/bold blue]")
    console.print(f"Directory: [dim]{scenarios_dir}[/dim]")
    
    if not scenarios_dir.exists():
        console.print(f"[red]Error:[/red] Directory not found: {scenarios_dir}")
        raise typer.Exit(1)
    
    scenario_files = list(scenarios_dir.glob("*.yml")) + list(scenarios_dir.glob("*.yaml"))
    
    if not scenario_files:
        console.print("[yellow]No scenario files found[/yellow]")
        return
    
    table = Table(title="Test Scenarios", show_header=True, header_style="bold magenta")
    table.add_column("Name", style="cyan", no_wrap=True)
    table.add_column("File", style="blue")
    table.add_column("Description", style="green")
    table.add_column("Tags", style="yellow")
    table.add_column("Priority", style="red")
    
    for file_path in sorted(scenario_files):
        try:
            # For now, use simple file parsing since we don't have full scenario loading
            name = file_path.stem
            description = "Test scenario"
            tags = "basic"
            priority = "medium"
            
            if filter_tag and filter_tag not in tags:
                continue
                
            table.add_row(name, file_path.name, description, tags, priority)
        except Exception as e:
            table.add_row(
                file_path.stem,
                file_path.name,
                f"[red]Error: {e}[/red]",
                "",
                ""
            )
    
    console.print(table)


@results_app.command("list")
def list_results(
    results_dir: Optional[Path] = typer.Option(
        None,
        "--dir",
        "-d",
        help="Results directory path"
    ),
    limit: int = typer.Option(
        10,
        "--limit",
        "-l",
        help="Maximum number of results to show"
    ),
) -> None:
    """List test results."""
    
    if results_dir is None:
        results_dir = Path("results")
    
    console.print(f"[bold blue]Test Results[/bold blue]")
    console.print(f"Directory: [dim]{results_dir}[/dim]")
    
    if not results_dir.exists():
        console.print("[yellow]No results directory found[/yellow]")
        return
    
    # Find result files
    result_files = list(results_dir.glob("*.json"))
    
    if not result_files:
        console.print("[yellow]No result files found[/yellow]")
        return
    
    # Sort by modification time (newest first)
    result_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
    
    table = Table(title="Recent Test Results", show_header=True)
    table.add_column("Timestamp", style="cyan")
    table.add_column("File", style="blue")
    table.add_column("Size", style="green")
    table.add_column("Status", style="yellow")
    
    for file_path in result_files[:limit]:
        try:
            stat = file_path.stat()
            timestamp = datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
            size = f"{stat.st_size:,} bytes"
            status = "completed"  # Would parse from file content
            
            table.add_row(timestamp, file_path.name, size, status)
        except Exception as e:
            table.add_row("", file_path.name, "", f"[red]Error: {e}[/red]")
    
    console.print(table)

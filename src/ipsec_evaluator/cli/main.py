"""
Main CLI application for IPsec Evaluator.
"""

import sys
from datetime import datetime
from pathlib import Path
from typing import List, Optional

import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.prompt import Confirm, Prompt
from rich.tree import Tree
from rich import print as rprint
from rich.syntax import Syntax

from ..models.config import TestConfiguration
from ..utils.logging import setup_logging, get_logger

# Create the main CLI app
app = typer.Typer(
    name="ipsec-evaluator",
    help="[bold blue]Modern IPsec compliance testing tool for ANSSI IPsecDR corpus[/bold blue]",
    add_completion=False,
    rich_markup_mode="rich",
)

# Create sub-commands
test_app = typer.Typer(help="Test execution commands")
results_app = typer.Typer(help="Results management commands")
scenario_app = typer.Typer(help="Scenario management commands")
config_app = typer.Typer(help="Configuration management commands")

app.add_typer(test_app, name="test")
app.add_typer(results_app, name="results")
app.add_typer(scenario_app, name="scenario")
app.add_typer(config_app, name="config")

console = Console()
logger = get_logger(__name__)


def print_banner():
    """Print the application banner."""
    banner = """
[bold blue]IPsec Evaluator[/bold blue] [dim]v0.1.0[/dim]
[italic]Modern IPsec compliance testing for ANSSI IPsecDR corpus[/italic]
"""
    console.print(Panel(banner, border_style="blue"))


@test_app.command("run")
def run_test(
    scenarios: List[str] = typer.Option(
        ...,
        "--scenario",
        "-s",
        help="Test scenario names to execute"
    ),
    config_file: Optional[Path] = typer.Option(
        None,
        "--config",
        "-c",
        help="Configuration file path"
    ),
    mode: str = typer.Option(
        "both",
        "--mode",
        "-m",
        help="Test mode: initiator, responder, or both"
    ),
    verbose: bool = typer.Option(
        False,
        "--verbose",
        "-v",
        help="Enable verbose output"
    ),
    output_dir: Optional[Path] = typer.Option(
        None,
        "--output",
        "-o",
        help="Output directory for results"
    ),
    dry_run: bool = typer.Option(
        False,
        "--dry-run",
        help="Show what would be executed without running tests"
    ),
) -> None:
    """Execute IPsec compliance tests."""

    print_banner()

    if not scenarios:
        console.print("[red]Error:[/red] No scenarios specified")
        raise typer.Exit(1)

    # Setup logging
    setup_logging(verbose=verbose)
    logger.info("Starting IPsec Evaluator test execution")

    # Load configuration
    config = _load_configuration(config_file)

    # Update config with CLI options
    config.global_config.verbose = verbose
    if output_dir:
        config.results_dir = output_dir

    # Display test plan
    _display_test_plan(scenarios, mode, config, dry_run)

    if dry_run:
        console.print("[yellow]Dry run completed - no tests executed[/yellow]")
        return

    # Confirm execution
    if not Confirm.ask("Execute these tests?"):
        console.print("[yellow]Test execution cancelled[/yellow]")
        return

    try:
        # Execute tests with progress tracking
        _execute_tests_with_progress(scenarios, mode, config)

    except KeyboardInterrupt:
        console.print("\n[yellow]Test execution interrupted by user[/yellow]")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]Error:[/red] {e}")
        logger.error(f"Test execution failed: {e}")
        raise typer.Exit(1)


@test_app.command("list")
def list_tests(
    scenarios_dir: Optional[Path] = typer.Option(
        None,
        "--dir",
        "-d",
        help="Scenarios directory path"
    ),
    filter_tag: Optional[str] = typer.Option(
        None,
        "--tag",
        "-t",
        help="Filter scenarios by tag"
    ),
) -> None:
    """List available test scenarios."""

    if scenarios_dir is None:
        scenarios_dir = Path("scenarios")

    console.print(f"[bold blue]Available Test Scenarios[/bold blue]")
    console.print(f"Directory: [dim]{scenarios_dir}[/dim]")

    if not scenarios_dir.exists():
        console.print(f"[red]Error:[/red] Directory not found: {scenarios_dir}")
        raise typer.Exit(1)

    scenario_files = list(scenarios_dir.glob("*.yml")) + list(scenarios_dir.glob("*.yaml"))

    if not scenario_files:
        console.print("[yellow]No scenario files found[/yellow]")
        return

    table = Table(title="Test Scenarios", show_header=True, header_style="bold magenta")
    table.add_column("Name", style="cyan", no_wrap=True)
    table.add_column("File", style="blue")
    table.add_column("Description", style="green")
    table.add_column("Tags", style="yellow")
    table.add_column("Priority", style="red")

    for file_path in sorted(scenario_files):
        try:
            # For now, use simple file parsing since we don't have full scenario loading
            name = file_path.stem
            description = "Test scenario"
            tags = "basic"
            priority = "medium"

            if filter_tag and filter_tag not in tags:
                continue

            table.add_row(name, file_path.name, description, tags, priority)
        except Exception as e:
            table.add_row(
                file_path.stem,
                file_path.name,
                f"[red]Error: {e}[/red]",
                "",
                ""
            )

    console.print(table)


@results_app.command("list")
def list_results(
    results_dir: Optional[Path] = typer.Option(
        None,
        "--dir",
        "-d",
        help="Results directory path"
    ),
    limit: int = typer.Option(
        10,
        "--limit",
        "-l",
        help="Maximum number of results to show"
    ),
) -> None:
    """List test results."""

    if results_dir is None:
        results_dir = Path("results")

    console.print(f"[bold blue]Test Results[/bold blue]")
    console.print(f"Directory: [dim]{results_dir}[/dim]")

    if not results_dir.exists():
        console.print("[yellow]No results directory found[/yellow]")
        return

    # Find result files
    result_files = list(results_dir.glob("*.json"))

    if not result_files:
        console.print("[yellow]No result files found[/yellow]")
        return

    # Sort by modification time (newest first)
    result_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

    table = Table(title="Recent Test Results", show_header=True)
    table.add_column("Timestamp", style="cyan")
    table.add_column("File", style="blue")
    table.add_column("Size", style="green")
    table.add_column("Status", style="yellow")

    for file_path in result_files[:limit]:
        try:
            stat = file_path.stat()
            timestamp = datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
            size = f"{stat.st_size:,} bytes"
            status = "completed"  # Would parse from file content

            table.add_row(timestamp, file_path.name, size, status)
        except Exception as e:
            table.add_row("", file_path.name, "", f"[red]Error: {e}[/red]")

    console.print(table)


@results_app.command("export")
def export_results(
    input_file: Path = typer.Argument(..., help="Result file to export"),
    output_file: Optional[Path] = typer.Option(
        None,
        "--output",
        "-o",
        help="Output file path"
    ),
    format: str = typer.Option(
        "markdown",
        "--format",
        "-f",
        help="Export format: markdown, latex, docx, html"
    ),
    template: Optional[Path] = typer.Option(
        None,
        "--template",
        "-t",
        help="Custom template file"
    ),
) -> None:
    """Export test results to various formats using pandoc."""

    if not input_file.exists():
        console.print(f"[red]Error:[/red] Input file not found: {input_file}")
        raise typer.Exit(1)

    if output_file is None:
        output_file = input_file.with_suffix(f".{format}")

    console.print(f"[blue]Exporting[/blue] {input_file} → {output_file} ([green]{format}[/green])")

    try:
        _export_with_pandoc(input_file, output_file, format, template)
        console.print(f"[green]✓[/green] Export completed: {output_file}")
    except Exception as e:
        console.print(f"[red]Export failed:[/red] {e}")
        raise typer.Exit(1)


@config_app.command("init")
def init_config(
    output_file: Optional[Path] = typer.Option(
        None,
        "--output",
        "-o",
        help="Output configuration file path"
    ),
    force: bool = typer.Option(
        False,
        "--force",
        "-f",
        help="Overwrite existing configuration file"
    ),
) -> None:
    """Initialize a new configuration file."""

    if output_file is None:
        output_file = Path("configs/default.yml")

    if output_file.exists() and not force:
        if not Confirm.ask(f"Configuration file {output_file} exists. Overwrite?"):
            console.print("[yellow]Configuration initialization cancelled[/yellow]")
            return

    # Create default configuration
    config = TestConfiguration()

    try:
        config.save_to_file(output_file)
        console.print(f"[green]✓[/green] Configuration file created: {output_file}")

        # Display configuration summary
        _display_config_summary(config)

    except Exception as e:
        console.print(f"[red]Failed to create configuration:[/red] {e}")
        raise typer.Exit(1)


@config_app.command("validate")
def validate_config(
    config_file: Path = typer.Argument(..., help="Configuration file to validate"),
) -> None:
    """Validate a configuration file."""

    if not config_file.exists():
        console.print(f"[red]Error:[/red] Configuration file not found: {config_file}")
        raise typer.Exit(1)

    try:
        config = TestConfiguration.from_file(config_file)
        console.print(f"[green]✓[/green] Configuration file is valid: {config_file}")

        # Check for ANSSI compliance issues
        issues = config.validate_crypto_config()
        if issues:
            console.print("\n[yellow]ANSSI Compliance Warnings:[/yellow]")
            for issue in issues:
                console.print(f"  [yellow]•[/yellow] {issue}")
        else:
            console.print("[green]✓[/green] Cryptographic configuration is ANSSI compliant")

        # Display configuration summary
        _display_config_summary(config)

    except Exception as e:
        console.print(f"[red]Configuration validation failed:[/red] {e}")
        raise typer.Exit(1)


@config_app.command("show")
def show_config(
    config_file: Optional[Path] = typer.Option(
        None,
        "--config",
        "-c",
        help="Configuration file path"
    ),
) -> None:
    """Display current configuration."""

    config = _load_configuration(config_file)
    _display_config_summary(config)


def _load_configuration(config_file: Optional[Path]) -> TestConfiguration:
    """Load test configuration."""
    if config_file and config_file.exists():
        console.print(f"[blue]Loading configuration:[/blue] {config_file}")
        try:
            config = TestConfiguration.from_file(config_file)
        except Exception as e:
            console.print(f"[red]Failed to load configuration:[/red] {e}")
            console.print("[yellow]Using default configuration[/yellow]")
            config = TestConfiguration()
    else:
        if config_file:
            console.print(f"[yellow]Configuration file not found:[/yellow] {config_file}")
        console.print("[blue]Using default configuration[/blue]")
        config = TestConfiguration()

    return config


def _display_config_summary(config: TestConfiguration):
    """Display configuration summary."""

    tree = Tree("[bold blue]Configuration Summary[/bold blue]")

    # Global settings
    global_node = tree.add("[blue]Global Settings[/blue]")
    global_node.add(f"Log Level: [yellow]{config.global_config.log_level}[/yellow]")
    global_node.add(f"Verbose: [yellow]{config.global_config.verbose}[/yellow]")
    global_node.add(f"Max Concurrent Tests: [yellow]{config.global_config.max_concurrent_tests}[/yellow]")

    # Network settings
    network_node = tree.add("[blue]Network Settings[/blue]")
    network_node.add(f"Initiator IP: [yellow]{config.network.initiator_ip}[/yellow]")
    network_node.add(f"Responder IP: [yellow]{config.network.responder_ip}[/yellow]")
    network_node.add(f"NAT Traversal: [yellow]{config.network.nat_traversal}[/yellow]")

    # Crypto settings
    crypto_node = tree.add("[blue]Cryptographic Settings[/blue]")
    crypto_node.add(f"IKE Encryption: [yellow]{', '.join(config.crypto.ike_encryption)}[/yellow]")
    crypto_node.add(f"IKE Integrity: [yellow]{', '.join(config.crypto.ike_integrity)}[/yellow]")
    crypto_node.add(f"DH Groups: [yellow]{', '.join(map(str, config.crypto.ike_dh_groups))}[/yellow]")

    # Directories
    dirs_node = tree.add("[blue]Directories[/blue]")
    dirs_node.add(f"Results: [yellow]{config.results_dir}[/yellow]")
    dirs_node.add(f"Scenarios: [yellow]{config.scenarios_dir}[/yellow]")
    dirs_node.add(f"Test Data: [yellow]{config.test_data_dir}[/yellow]")

    console.print(Panel(tree, border_style="blue"))


def _display_test_plan(scenarios: List[str], mode: str, config: TestConfiguration, dry_run: bool):
    """Display the test execution plan."""

    panel_title = "[bold]Test Execution Plan[/bold]" + (" [yellow](DRY RUN)[/yellow]" if dry_run else "")

    tree = Tree(panel_title)

    # Add scenarios
    scenarios_node = tree.add("[blue]Scenarios[/blue]")
    for scenario in scenarios:
        scenarios_node.add(f"[green]{scenario}[/green]")

    # Add configuration
    config_node = tree.add("[blue]Configuration[/blue]")
    config_node.add(f"Mode: [yellow]{mode}[/yellow]")
    config_node.add(f"Output: [yellow]{config.results_dir}[/yellow]")
    config_node.add(f"Verbose: [yellow]{config.global_config.verbose}[/yellow]")

    console.print(Panel(tree, border_style="blue"))


def _execute_tests_with_progress(scenarios: List[str], mode: str, config: TestConfiguration):
    """Execute tests with Rich progress tracking."""

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        console=console,
    ) as progress:

        # Create main task
        main_task = progress.add_task("Executing tests...", total=len(scenarios))

        for i, scenario in enumerate(scenarios):
            # Update current scenario
            progress.update(main_task, description=f"Running {scenario}")

            # Simulate test execution (replace with actual orchestrator call)
            import time
            time.sleep(1)  # Simulate work

            progress.advance(main_task)

        progress.update(main_task, description="[green]All tests completed![/green]")

    console.print("[green]✓[/green] Test execution completed successfully")


def _export_with_pandoc(input_file: Path, output_file: Path, format: str, template: Optional[Path]):
    """Export results using pandoc."""
    import subprocess

    # Build pandoc command
    cmd = ["pandoc", str(input_file), "-o", str(output_file)]

    # Add format-specific options
    if format == "latex":
        cmd.extend(["--to", "latex"])
    elif format == "docx":
        cmd.extend(["--to", "docx"])
    elif format == "html":
        cmd.extend(["--to", "html", "--standalone"])
    elif format == "markdown":
        cmd.extend(["--to", "markdown"])

    # Add template if specified
    if template:
        cmd.extend(["--template", str(template)])

    # Execute pandoc
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info(f"Pandoc export successful: {' '.join(cmd)}")
    except subprocess.CalledProcessError as e:
        raise Exception(f"Pandoc failed: {e.stderr}")
    except FileNotFoundError:
        raise Exception("Pandoc not found. Please install pandoc to use export functionality.")


@app.callback()
def main(
    version: bool = typer.Option(False, "--version", help="Show version information"),
):
    """
    IPsec Evaluator - Modern IPsec compliance testing tool.

    A professional tool for testing IPsec implementations against ANSSI requirements.
    """
    if version:
        console.print("IPsec Evaluator v0.1.0")
        raise typer.Exit()


if __name__ == "__main__":
    app()

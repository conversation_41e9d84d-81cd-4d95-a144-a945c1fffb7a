"""
Test execution engine for IPsec scenarios.

The Tester is responsible for executing individual test scenarios,
managing IPsec endpoints, and collecting test data.
"""

import asyncio
from datetime import datetime
from typing import Dict, Any, Optional

from ..models.base import TestMode
from ..models.config import TestConfiguration
from ..models.scenario import TestScenario
from ..models.results import TestData, ExchangeAnalysis, PacketAnalysis
from ..utils.logging import get_logger
from .hooks import HookManager

logger = get_logger(__name__)


class Tester:
    """
    Executes individual test scenarios.
    
    The Tester manages the execution of a single test scenario,
    including IPsec endpoint deployment, hook execution, and
    data collection.
    """
    
    def __init__(
        self,
        test_id: str,
        config: TestConfiguration,
        scenario: TestScenario,
        mode: TestMode
    ):
        """
        Initialize the tester.
        
        Args:
            test_id: Unique test identifier
            config: Test configuration
            scenario: Test scenario to execute
            mode: Test mode (initiator/responder)
        """
        self.test_id = test_id
        self.config = config
        self.scenario = scenario
        self.mode = mode
        
        self.hook_manager = HookManager()
        self.test_data = TestData()
        
        logger.info(f"Tester initialized for test {test_id} in {mode} mode")
    
    async def execute(self) -> TestData:
        """
        Execute the test scenario.
        
        Returns:
            Collected test data
        """
        logger.info(f"Starting test execution: {self.test_id}")
        
        try:
            # Setup hooks
            await self._setup_hooks()
            
            # Deploy infrastructure
            await self._deploy_infrastructure()
            
            # Execute test scenario
            await self._execute_scenario()
            
            # Collect results
            await self._collect_results()
            
            logger.info(f"Test execution completed: {self.test_id}")
            return self.test_data
            
        except Exception as e:
            logger.error(f"Test execution failed: {self.test_id} - {e}")
            raise
        finally:
            # Cleanup
            await self._cleanup()
    
    async def _setup_hooks(self) -> None:
        """Setup hooks for the test scenario."""
        logger.debug(f"Setting up hooks for test {self.test_id}")
        
        # Get scenario definition for the current mode
        scenario_name = list(self.scenario.test_scenarios.initiator.keys())[0] if self.mode == TestMode.INITIATOR else list(self.scenario.test_scenarios.responder.keys())[0]
        scenario_def = self.scenario.get_scenario_for_mode(self.mode, scenario_name)
        
        if not scenario_def:
            logger.warning(f"No scenario definition found for mode {self.mode}")
            return
        
        # Register hooks
        for hook_def in scenario_def.hooks.packet_callbacks:
            await self.hook_manager.register_packet_hook(
                packet_number=hook_def.packet,
                callback_name=hook_def.callback,
                exchange_type=hook_def.exchange
            )
        
        for hook_def in scenario_def.hooks.exchange_callbacks:
            await self.hook_manager.register_exchange_hook(
                exchange_type=hook_def.exchange,
                callback_name=hook_def.callback
            )
        
        if scenario_def.hooks.universal_callback:
            await self.hook_manager.register_universal_hook(
                callback_name=scenario_def.hooks.universal_callback
            )
        
        logger.debug(f"Hooks setup completed for test {self.test_id}")
    
    async def _deploy_infrastructure(self) -> None:
        """Deploy test infrastructure."""
        logger.debug(f"Deploying infrastructure for test {self.test_id}")
        
        # TODO: Implement infrastructure deployment
        # This will use Incus containers and OpenTofu
        
        # For now, simulate deployment
        await asyncio.sleep(1)
        
        logger.debug(f"Infrastructure deployment completed for test {self.test_id}")
    
    async def _execute_scenario(self) -> None:
        """Execute the test scenario."""
        logger.debug(f"Executing scenario for test {self.test_id}")
        
        # Get scenario definition
        scenario_name = list(self.scenario.test_scenarios.initiator.keys())[0] if self.mode == TestMode.INITIATOR else list(self.scenario.test_scenarios.responder.keys())[0]
        scenario_def = self.scenario.get_scenario_for_mode(self.mode, scenario_name)
        
        if not scenario_def:
            raise ValueError(f"No scenario definition found for mode {self.mode}")
        
        # Execute exchanges
        for exchange_def in scenario_def.exchanges:
            await self._execute_exchange(exchange_def)
        
        logger.debug(f"Scenario execution completed for test {self.test_id}")
    
    async def _execute_exchange(self, exchange_def) -> None:
        """Execute a single exchange."""
        logger.debug(f"Executing {exchange_def.type} exchange for test {self.test_id}")
        
        start_time = datetime.utcnow()
        exchange_analysis = ExchangeAnalysis(
            exchange_type=exchange_def.type.value,
            start_time=start_time
        )
        
        try:
            # Simulate exchange execution
            for packet_num in range(1, exchange_def.expected_packets + 1):
                packet_analysis = await self._process_packet(
                    packet_num, exchange_def.type.value, start_time
                )
                exchange_analysis.add_packet(packet_analysis)
                
                # Trigger packet hooks
                await self.hook_manager.trigger_packet_hook(
                    packet_num, exchange_def.type.value, packet_analysis
                )
            
            # Trigger exchange hooks
            await self.hook_manager.trigger_exchange_hook(
                exchange_def.type.value, exchange_analysis
            )
            
            exchange_analysis.successful = True
            
        except Exception as e:
            logger.error(f"Exchange {exchange_def.type} failed: {e}")
            exchange_analysis.successful = False
            raise
        finally:
            exchange_analysis.end_time = datetime.utcnow()
            if exchange_analysis.end_time:
                exchange_analysis.duration = (
                    exchange_analysis.end_time - exchange_analysis.start_time
                ).total_seconds()
            
            self.test_data.add_exchange(exchange_analysis)
    
    async def _process_packet(
        self, 
        packet_num: int, 
        exchange_type: str, 
        base_time: datetime
    ) -> PacketAnalysis:
        """Process a single packet."""
        # Simulate packet processing
        packet_time = datetime.utcnow()
        
        packet_analysis = PacketAnalysis(
            packet_number=packet_num,
            exchange_type=exchange_type,
            timestamp=packet_time,
            size=1024,  # Simulated size
            source_ip=self.config.network.initiator_ip if self.mode == TestMode.INITIATOR else self.config.network.responder_ip,
            destination_ip=self.config.network.responder_ip if self.mode == TestMode.INITIATOR else self.config.network.initiator_ip,
            algorithms_used={"encryption": "AES_GCM_16", "integrity": "HMAC_SHA2_256"},
            key_sizes={"encryption": 256, "integrity": 256},
            anssi_compliant=True
        )
        
        # Trigger universal hooks
        await self.hook_manager.trigger_universal_hook(packet_analysis)
        
        return packet_analysis
    
    async def _collect_results(self) -> None:
        """Collect test results and metadata."""
        logger.debug(f"Collecting results for test {self.test_id}")
        
        # Store test configuration
        self.test_data.test_configuration = {
            "scenario_name": self.scenario.name,
            "mode": self.mode.value,
            "crypto_config": self.config.crypto.dict(),
            "network_config": self.config.network.dict()
        }
        
        # Collect hook results
        hook_results = await self.hook_manager.get_results()
        for hook_result in hook_results:
            self.test_data.add_hook_result(hook_result)
        
        logger.debug(f"Results collection completed for test {self.test_id}")
    
    async def _cleanup(self) -> None:
        """Cleanup test resources."""
        logger.debug(f"Cleaning up resources for test {self.test_id}")
        
        # TODO: Implement infrastructure cleanup
        # This will clean up Incus containers and network resources
        
        # For now, simulate cleanup
        await asyncio.sleep(0.5)
        
        logger.debug(f"Cleanup completed for test {self.test_id}")

"""
Compliance checker for IPsec test results.

The Checker analyzes test data and determines compliance with
ANSSI requirements and IPsec standards.
"""

from typing import Dict, List

from ..models.scenario import TestScenario
from ..models.config import TestConfiguration
from ..models.results import TestData, ComplianceResult, ComplianceLevel
from ..core.ikev2.constants import ANSSI_APPROVED_ALGORITHMS
from ..utils.logging import get_logger

logger = get_logger(__name__)


class Checker:
    """
    Analyzes test results for compliance.
    
    The Checker examines test data and determines compliance
    with ANSSI requirements and IPsec standards.
    """
    
    def __init__(
        self,
        test_id: str,
        config: TestConfiguration,
        scenario: TestScenario
    ):
        """
        Initialize the checker.
        
        Args:
            test_id: Unique test identifier
            config: Test configuration
            scenario: Test scenario definition
        """
        self.test_id = test_id
        self.config = config
        self.scenario = scenario
        
        logger.info(f"Checker initialized for test {test_id}")
    
    async def check_compliance(self, test_data: TestData) -> ComplianceResult:
        """
        Check compliance of test results.
        
        Args:
            test_data: Test execution data
            
        Returns:
            Compliance analysis result
        """
        logger.info(f"Starting compliance check for test {self.test_id}")
        
        result = ComplianceResult(level=ComplianceLevel.UNKNOWN)
        
        try:
            # Check algorithm compliance
            await self._check_algorithm_compliance(test_data, result)
            
            # Check key size compliance
            await self._check_key_size_compliance(test_data, result)
            
            # Check protocol compliance
            await self._check_protocol_compliance(test_data, result)
            
            # Check ANSSI-specific requirements
            await self._check_anssi_requirements(test_data, result)
            
            # Calculate overall compliance
            self._calculate_overall_compliance(result)
            
            logger.info(f"Compliance check completed: {result.level} ({result.score:.1f}%)")
            return result
            
        except Exception as e:
            logger.error(f"Compliance check failed for test {self.test_id}: {e}")
            result.level = ComplianceLevel.UNKNOWN
            result.issues.append(f"Compliance check failed: {e}")
            return result
    
    async def _check_algorithm_compliance(
        self, 
        test_data: TestData, 
        result: ComplianceResult
    ) -> None:
        """Check cryptographic algorithm compliance."""
        logger.debug("Checking algorithm compliance")
        
        for exchange in test_data.exchanges:
            for packet in exchange.packets:
                # Check encryption algorithms
                if "encryption" in packet.algorithms_used:
                    alg = packet.algorithms_used["encryption"]
                    is_compliant = self._is_algorithm_anssi_approved("encryption", alg)
                    result.algorithm_compliance[f"encryption_{packet.packet_number}"] = is_compliant
                    
                    if not is_compliant:
                        result.issues.append(f"Non-ANSSI encryption algorithm: {alg}")
                
                # Check integrity algorithms
                if "integrity" in packet.algorithms_used:
                    alg = packet.algorithms_used["integrity"]
                    is_compliant = self._is_algorithm_anssi_approved("integrity", alg)
                    result.algorithm_compliance[f"integrity_{packet.packet_number}"] = is_compliant
                    
                    if not is_compliant:
                        result.issues.append(f"Non-ANSSI integrity algorithm: {alg}")
    
    async def _check_key_size_compliance(
        self, 
        test_data: TestData, 
        result: ComplianceResult
    ) -> None:
        """Check cryptographic key size compliance."""
        logger.debug("Checking key size compliance")
        
        for exchange in test_data.exchanges:
            for packet in exchange.packets:
                # Check encryption key sizes
                if "encryption" in packet.key_sizes:
                    key_size = packet.key_sizes["encryption"]
                    is_compliant = key_size >= 256  # ANSSI minimum
                    result.key_size_compliance[f"encryption_{packet.packet_number}"] = is_compliant
                    
                    if not is_compliant:
                        result.issues.append(f"Insufficient encryption key size: {key_size} bits")
                
                # Check integrity key sizes
                if "integrity" in packet.key_sizes:
                    key_size = packet.key_sizes["integrity"]
                    is_compliant = key_size >= 256  # ANSSI minimum
                    result.key_size_compliance[f"integrity_{packet.packet_number}"] = is_compliant
                    
                    if not is_compliant:
                        result.issues.append(f"Insufficient integrity key size: {key_size} bits")
    
    async def _check_protocol_compliance(
        self, 
        test_data: TestData, 
        result: ComplianceResult
    ) -> None:
        """Check IPsec protocol compliance."""
        logger.debug("Checking protocol compliance")
        
        # Check exchange completeness
        expected_exchanges = ["IKE_SA_INIT", "IKE_AUTH"]
        found_exchanges = [ex.exchange_type for ex in test_data.exchanges]
        
        for expected in expected_exchanges:
            is_present = expected in found_exchanges
            result.protocol_compliance[f"exchange_{expected}"] = is_present
            
            if not is_present:
                result.issues.append(f"Missing required exchange: {expected}")
        
        # Check exchange success
        for exchange in test_data.exchanges:
            is_successful = exchange.successful
            result.protocol_compliance[f"success_{exchange.exchange_type}"] = is_successful
            
            if not is_successful:
                result.issues.append(f"Exchange failed: {exchange.exchange_type}")
    
    async def _check_anssi_requirements(
        self, 
        test_data: TestData, 
        result: ComplianceResult
    ) -> None:
        """Check ANSSI-specific requirements."""
        logger.debug("Checking ANSSI requirements")
        
        # Check for forbidden algorithms
        forbidden_algorithms = ["DES", "3DES", "MD5", "SHA1"]
        
        for exchange in test_data.exchanges:
            for packet in exchange.packets:
                for alg_type, algorithm in packet.algorithms_used.items():
                    for forbidden in forbidden_algorithms:
                        if forbidden.lower() in algorithm.lower():
                            result.anssi_checks[f"no_forbidden_{forbidden}"] = False
                            result.issues.append(f"Forbidden algorithm detected: {algorithm}")
                            break
                    else:
                        result.anssi_checks[f"no_forbidden_{alg_type}"] = True
        
        # Check perfect forward secrecy
        has_dh_exchange = any(
            "DH" in ex.algorithms_negotiated or "ECDH" in ex.algorithms_negotiated
            for ex in test_data.exchanges
        )
        result.anssi_checks["perfect_forward_secrecy"] = has_dh_exchange
        
        if not has_dh_exchange:
            result.issues.append("Perfect Forward Secrecy not ensured")
        
        # Check certificate validation (if applicable)
        has_cert_auth = any(
            "certificate" in ex.compliance_details
            for ex in test_data.exchanges
            if ex.compliance_details
        )
        if has_cert_auth:
            result.anssi_checks["certificate_validation"] = True
        else:
            result.warnings.append("Certificate authentication not detected")
    
    def _is_algorithm_anssi_approved(self, alg_type: str, algorithm: str) -> bool:
        """Check if algorithm is ANSSI approved."""
        approved_algs = ANSSI_APPROVED_ALGORITHMS.get(alg_type, [])
        
        # Convert algorithm name to enum value for comparison
        # This is a simplified check - in practice, you'd need proper mapping
        algorithm_upper = algorithm.upper()
        
        if alg_type == "encryption":
            return any(
                alg_name in algorithm_upper 
                for alg_name in ["AES_GCM", "AES_CBC", "CHACHA20"]
            )
        elif alg_type == "integrity":
            return any(
                alg_name in algorithm_upper
                for alg_name in ["HMAC_SHA2_256", "HMAC_SHA2_384", "HMAC_SHA2_512"]
            )
        
        return False
    
    def _calculate_overall_compliance(self, result: ComplianceResult) -> None:
        """Calculate overall compliance level and score."""
        total_checks = 0
        passed_checks = 0
        
        # Count algorithm compliance
        for passed in result.algorithm_compliance.values():
            total_checks += 1
            if passed:
                passed_checks += 1
        
        # Count key size compliance
        for passed in result.key_size_compliance.values():
            total_checks += 1
            if passed:
                passed_checks += 1
        
        # Count protocol compliance
        for passed in result.protocol_compliance.values():
            total_checks += 1
            if passed:
                passed_checks += 1
        
        # Count ANSSI checks
        for passed in result.anssi_checks.values():
            total_checks += 1
            if passed:
                passed_checks += 1
        
        # Calculate scores
        if total_checks > 0:
            result.score = (passed_checks / total_checks) * 100
            result.anssi_score = result.score  # Simplified for now
        
        # Determine compliance level
        if result.score >= 95:
            result.level = ComplianceLevel.COMPLIANT
        elif result.score >= 70:
            result.level = ComplianceLevel.PARTIAL
        else:
            result.level = ComplianceLevel.NON_COMPLIANT
        
        # Add recommendations
        if result.score < 100:
            result.recommendations.append("Review and address identified compliance issues")
        
        if len(result.issues) > 0:
            result.recommendations.append("Fix critical compliance violations")
        
        if len(result.warnings) > 0:
            result.recommendations.append("Address compliance warnings for better security")

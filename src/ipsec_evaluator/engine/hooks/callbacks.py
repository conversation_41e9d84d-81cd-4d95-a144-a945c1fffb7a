"""
Callback registry and default callback implementations.
"""

from typing import Callable, Dict, Optional, Any
import json

from ...utils.logging import get_logger

logger = get_logger(__name__)


class CallbackRegistry:
    """
    Registry for callback functions.
    
    Manages registration and retrieval of callback functions
    used by the hook system.
    """
    
    def __init__(self):
        """Initialize the callback registry."""
        self._callbacks: Dict[str, Callable] = {}
        self._register_default_callbacks()
        
        logger.debug("CallbackRegistry initialized")
    
    def register_callback(self, name: str, callback: Callable) -> None:
        """
        Register a callback function.
        
        Args:
            name: Callback name
            callback: Callback function
        """
        self._callbacks[name] = callback
        logger.debug(f"Registered callback: {name}")
    
    def get_callback(self, name: str) -> Optional[Callable]:
        """
        Get a callback function by name.
        
        Args:
            name: Callback name
            
        Returns:
            Callback function or None if not found
        """
        return self._callbacks.get(name)
    
    def list_callbacks(self) -> list:
        """Get list of registered callback names."""
        return list(self._callbacks.keys())
    
    def _register_default_callbacks(self) -> None:
        """Register default callback implementations."""
        
        # Validation callbacks
        self.register_callback("validate_init_proposal", validate_init_proposal)
        self.register_callback("validate_auth_method", validate_auth_method)
        self.register_callback("validate_received_proposal", validate_received_proposal)
        self.register_callback("validate_auth_payload", validate_auth_payload)
        
        # Check callbacks
        self.register_callback("check_ike_sa_algorithms", check_ike_sa_algorithms)
        self.register_callback("check_authentication", check_authentication)
        self.register_callback("check_child_sa_algorithms", check_child_sa_algorithms)
        self.register_callback("respond_with_compliant_proposal", respond_with_compliant_proposal)
        self.register_callback("perform_authentication", perform_authentication)
        
        # Monitoring callbacks
        self.register_callback("log_all_packets", log_all_packets)
        self.register_callback("monitor_all_traffic", monitor_all_traffic)


# Default callback implementations

def validate_init_proposal(**kwargs) -> Dict[str, Any]:
    """Validate initial IKE proposal for ANSSI compliance."""
    packet_analysis = kwargs.get("packet_analysis")
    
    logger.info("Validating initial proposal")
    
    result = {
        "validation_passed": True,
        "issues": [],
        "algorithms_checked": packet_analysis.algorithms_used if packet_analysis else {}
    }
    
    if packet_analysis:
        # Check for ANSSI-approved algorithms
        algorithms = packet_analysis.algorithms_used
        
        if "encryption" in algorithms:
            if "AES" not in algorithms["encryption"]:
                result["validation_passed"] = False
                result["issues"].append("Non-ANSSI encryption algorithm")
        
        if "integrity" in algorithms:
            if "HMAC_SHA2" not in algorithms["integrity"]:
                result["validation_passed"] = False
                result["issues"].append("Non-ANSSI integrity algorithm")
    
    logger.info(f"Initial proposal validation: {'PASSED' if result['validation_passed'] else 'FAILED'}")
    return result


def validate_auth_method(**kwargs) -> Dict[str, Any]:
    """Validate authentication method compliance."""
    packet_analysis = kwargs.get("packet_analysis")
    
    logger.info("Validating authentication method")
    
    result = {
        "validation_passed": True,
        "auth_method": "certificate",  # Simulated
        "issues": []
    }
    
    # Simulate authentication method validation
    # In practice, this would examine the actual AUTH payload
    
    logger.info("Authentication method validation: PASSED")
    return result


def validate_received_proposal(**kwargs) -> Dict[str, Any]:
    """Validate received proposal from initiator."""
    packet_analysis = kwargs.get("packet_analysis")
    
    logger.info("Validating received proposal")
    
    result = {
        "validation_passed": True,
        "proposal_acceptable": True,
        "selected_algorithms": packet_analysis.algorithms_used if packet_analysis else {}
    }
    
    logger.info("Received proposal validation: PASSED")
    return result


def validate_auth_payload(**kwargs) -> Dict[str, Any]:
    """Validate authentication payload."""
    packet_analysis = kwargs.get("packet_analysis")
    
    logger.info("Validating authentication payload")
    
    result = {
        "validation_passed": True,
        "auth_verified": True,
        "certificate_valid": True
    }
    
    logger.info("Authentication payload validation: PASSED")
    return result


def check_ike_sa_algorithms(**kwargs) -> Dict[str, Any]:
    """Check IKE SA algorithm compliance."""
    exchange_analysis = kwargs.get("exchange_analysis")
    
    logger.info("Checking IKE SA algorithms")
    
    result = {
        "algorithms_compliant": True,
        "negotiated_algorithms": {},
        "compliance_issues": []
    }
    
    if exchange_analysis:
        # Analyze negotiated algorithms
        result["negotiated_algorithms"] = exchange_analysis.algorithms_negotiated
    
    logger.info("IKE SA algorithm check: COMPLIANT")
    return result


def check_authentication(**kwargs) -> Dict[str, Any]:
    """Check authentication process compliance."""
    exchange_analysis = kwargs.get("exchange_analysis")
    
    logger.info("Checking authentication process")
    
    result = {
        "authentication_successful": True,
        "method_compliant": True,
        "certificate_chain_valid": True
    }
    
    logger.info("Authentication check: COMPLIANT")
    return result


def check_child_sa_algorithms(**kwargs) -> Dict[str, Any]:
    """Check Child SA algorithm compliance."""
    exchange_analysis = kwargs.get("exchange_analysis")
    
    logger.info("Checking Child SA algorithms")
    
    result = {
        "algorithms_compliant": True,
        "esp_algorithms": {},
        "compliance_issues": []
    }
    
    logger.info("Child SA algorithm check: COMPLIANT")
    return result


def respond_with_compliant_proposal(**kwargs) -> Dict[str, Any]:
    """Generate compliant proposal response."""
    exchange_analysis = kwargs.get("exchange_analysis")
    
    logger.info("Generating compliant proposal response")
    
    result = {
        "proposal_generated": True,
        "algorithms_selected": {
            "encryption": "AES_GCM_16",
            "integrity": "HMAC_SHA2_256",
            "prf": "PRF_HMAC_SHA2_256",
            "dh_group": 19
        }
    }
    
    logger.info("Compliant proposal response generated")
    return result


def perform_authentication(**kwargs) -> Dict[str, Any]:
    """Perform authentication process."""
    exchange_analysis = kwargs.get("exchange_analysis")
    
    logger.info("Performing authentication")
    
    result = {
        "authentication_completed": True,
        "method_used": "certificate",
        "success": True
    }
    
    logger.info("Authentication performed successfully")
    return result


def log_all_packets(**kwargs) -> Dict[str, Any]:
    """Log all packet information."""
    packet_analysis = kwargs.get("packet_analysis")
    
    if packet_analysis:
        logger.info(f"Packet logged: #{packet_analysis.packet_number} "
                   f"{packet_analysis.exchange_type} "
                   f"{packet_analysis.source_ip} -> {packet_analysis.destination_ip} "
                   f"({packet_analysis.size} bytes)")
    
    return {"logged": True, "timestamp": packet_analysis.timestamp if packet_analysis else None}


def monitor_all_traffic(**kwargs) -> Dict[str, Any]:
    """Monitor all network traffic."""
    packet_analysis = kwargs.get("packet_analysis")
    
    if packet_analysis:
        logger.debug(f"Traffic monitored: {packet_analysis.exchange_type} packet "
                    f"with algorithms {packet_analysis.algorithms_used}")
    
    return {
        "monitored": True,
        "traffic_summary": {
            "packet_count": 1,
            "algorithms_seen": packet_analysis.algorithms_used if packet_analysis else {}
        }
    }

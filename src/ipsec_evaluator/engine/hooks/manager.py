"""
Hook manager for coordinating callback execution.
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any

from ...models.base import HookType
from ...models.results import HookResult, PacketAnalysis, ExchangeAnalysis
from ...utils.logging import get_logger
from .callbacks import CallbackRegistry

logger = get_logger(__name__)


class HookManager:
    """
    Manages hook registration and execution.
    
    The HookManager coordinates the execution of callbacks at
    specific points during test execution.
    """
    
    def __init__(self):
        """Initialize the hook manager."""
        self.callback_registry = CallbackRegistry()
        self.packet_hooks: Dict[int, List[str]] = {}
        self.exchange_hooks: Dict[str, List[str]] = {}
        self.universal_hooks: List[str] = []
        self.hook_results: List[HookResult] = []
        
        logger.debug("HookManager initialized")
    
    async def register_packet_hook(
        self, 
        packet_number: int, 
        callback_name: str,
        exchange_type: Optional[str] = None
    ) -> None:
        """
        Register a packet-specific hook.
        
        Args:
            packet_number: Packet number to hook
            callback_name: Name of callback function
            exchange_type: Optional exchange type filter
        """
        if packet_number not in self.packet_hooks:
            self.packet_hooks[packet_number] = []
        
        self.packet_hooks[packet_number].append(callback_name)
        logger.debug(f"Registered packet hook: packet {packet_number} -> {callback_name}")
    
    async def register_exchange_hook(
        self, 
        exchange_type: str, 
        callback_name: str
    ) -> None:
        """
        Register an exchange-specific hook.
        
        Args:
            exchange_type: Exchange type to hook
            callback_name: Name of callback function
        """
        if exchange_type not in self.exchange_hooks:
            self.exchange_hooks[exchange_type] = []
        
        self.exchange_hooks[exchange_type].append(callback_name)
        logger.debug(f"Registered exchange hook: {exchange_type} -> {callback_name}")
    
    async def register_universal_hook(self, callback_name: str) -> None:
        """
        Register a universal hook that triggers on every packet.
        
        Args:
            callback_name: Name of callback function
        """
        self.universal_hooks.append(callback_name)
        logger.debug(f"Registered universal hook: {callback_name}")
    
    async def trigger_packet_hook(
        self, 
        packet_number: int, 
        exchange_type: str,
        packet_analysis: PacketAnalysis
    ) -> None:
        """
        Trigger hooks for a specific packet.
        
        Args:
            packet_number: Packet number
            exchange_type: Exchange type
            packet_analysis: Packet analysis data
        """
        if packet_number in self.packet_hooks:
            for callback_name in self.packet_hooks[packet_number]:
                await self._execute_callback(
                    callback_name,
                    HookType.PACKET_NUMBER,
                    {
                        "packet_number": packet_number,
                        "exchange_type": exchange_type,
                        "packet_analysis": packet_analysis
                    }
                )
    
    async def trigger_exchange_hook(
        self, 
        exchange_type: str,
        exchange_analysis: ExchangeAnalysis
    ) -> None:
        """
        Trigger hooks for a specific exchange.
        
        Args:
            exchange_type: Exchange type
            exchange_analysis: Exchange analysis data
        """
        if exchange_type in self.exchange_hooks:
            for callback_name in self.exchange_hooks[exchange_type]:
                await self._execute_callback(
                    callback_name,
                    HookType.EXCHANGE_STEP,
                    {
                        "exchange_type": exchange_type,
                        "exchange_analysis": exchange_analysis
                    }
                )
    
    async def trigger_universal_hook(self, packet_analysis: PacketAnalysis) -> None:
        """
        Trigger universal hooks for every packet.
        
        Args:
            packet_analysis: Packet analysis data
        """
        for callback_name in self.universal_hooks:
            await self._execute_callback(
                callback_name,
                HookType.UNIVERSAL,
                {
                    "packet_analysis": packet_analysis
                }
            )
    
    async def _execute_callback(
        self, 
        callback_name: str, 
        hook_type: HookType,
        input_data: Dict[str, Any]
    ) -> None:
        """
        Execute a callback function.
        
        Args:
            callback_name: Name of callback function
            hook_type: Type of hook
            input_data: Input data for callback
        """
        start_time = datetime.utcnow()
        hook_result = HookResult(
            hook_name=callback_name,
            hook_type=hook_type.value,
            execution_time=start_time,
            success=False,
            input_data=input_data
        )
        
        try:
            logger.debug(f"Executing callback: {callback_name}")
            
            # Get callback function
            callback_func = self.callback_registry.get_callback(callback_name)
            if not callback_func:
                raise ValueError(f"Callback not found: {callback_name}")
            
            # Execute callback
            if asyncio.iscoroutinefunction(callback_func):
                output_data = await callback_func(**input_data)
            else:
                output_data = callback_func(**input_data)
            
            hook_result.success = True
            hook_result.output_data = output_data or {}
            
            logger.debug(f"Callback executed successfully: {callback_name}")
            
        except Exception as e:
            logger.error(f"Callback execution failed: {callback_name} - {e}")
            hook_result.error_message = str(e)
            hook_result.stack_trace = str(e.__traceback__)
        
        finally:
            end_time = datetime.utcnow()
            hook_result.duration = (end_time - start_time).total_seconds()
            self.hook_results.append(hook_result)
    
    async def get_results(self) -> List[HookResult]:
        """
        Get all hook execution results.
        
        Returns:
            List of hook results
        """
        return self.hook_results.copy()
    
    def clear_results(self) -> None:
        """Clear all hook results."""
        self.hook_results.clear()
        logger.debug("Hook results cleared")

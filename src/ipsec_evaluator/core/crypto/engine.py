"""
Main cryptographic engine for IPsec operations.

This module provides the central CryptoEngine class that coordinates
all cryptographic operations used in IPsec communications.
"""

import secrets
from typing import Dict, List, Optional, Tuple

from cryptography.hazmat.backends import default_backend

from .algorithms import EncryptionAlgorithm, IntegrityAlgorithm, PRFAlgorithm
from .keys import KeyManager
from .dh import DHManager


class CryptoEngine:
    """
    Central cryptographic engine for IPsec operations.

    Manages all cryptographic operations including:
    - Encryption/decryption
    - Integrity protection and verification
    - Key derivation
    - Digital signatures
    - Di<PERSON>ie-<PERSON><PERSON> key exchange
    """

    def __init__(self):
        self.key_manager = KeyManager()
        self.dh_manager = DHManager()
        self._backend = default_backend()

        # Algorithm instances
        self._encryption_algs: Dict[str, EncryptionAlgorithm] = {}
        self._integrity_algs: Dict[str, IntegrityAlgorithm] = {}
        self._prf_algs: Dict[str, PRFAlgorithm] = {}

        self._initialize_algorithms()

    def _initialize_algorithms(self) -> None:
        """Initialize supported cryptographic algorithms."""
        # Initialize encryption algorithms
        self._encryption_algs.update({
            "AES_GCM_16": EncryptionAlgorithm(
                name="AES_GCM_16",
                algorithm_id=20,
                key_sizes=[128, 192, 256],
                block_size=16,
                iv_size=8,
                is_aead=True
            ),
            "AES_CBC": EncryptionAlgorithm(
                name="AES_CBC",
                algorithm_id=12,
                key_sizes=[128, 192, 256],
                block_size=16,
                iv_size=16,
                is_aead=False
            ),
            "CHACHA20_POLY1305": EncryptionAlgorithm(
                name="CHACHA20_POLY1305",
                algorithm_id=28,
                key_sizes=[256],
                block_size=1,
                iv_size=8,
                is_aead=True
            ),
        })

        # Initialize integrity algorithms
        self._integrity_algs.update({
            "HMAC_SHA2_256": IntegrityAlgorithm(
                name="HMAC_SHA2_256",
                algorithm_id=12,
                key_size=32,
                hash_size=16,
                hash_algorithm_name="SHA256"
            ),
            "HMAC_SHA2_384": IntegrityAlgorithm(
                name="HMAC_SHA2_384",
                algorithm_id=13,
                key_size=48,
                hash_size=24,
                hash_algorithm_name="SHA384"
            ),
            "HMAC_SHA2_512": IntegrityAlgorithm(
                name="HMAC_SHA2_512",
                algorithm_id=14,
                key_size=64,
                hash_size=32,
                hash_algorithm_name="SHA512"
            ),
        })

        # Initialize PRF algorithms
        self._prf_algs.update({
            "PRF_HMAC_SHA2_256": PRFAlgorithm(
                name="PRF_HMAC_SHA2_256",
                algorithm_id=5,
                key_size=32,
                hash_algorithm_name="SHA256"
            ),
            "PRF_HMAC_SHA2_384": PRFAlgorithm(
                name="PRF_HMAC_SHA2_384",
                algorithm_id=6,
                key_size=48,
                hash_algorithm_name="SHA384"
            ),
            "PRF_HMAC_SHA2_512": PRFAlgorithm(
                name="PRF_HMAC_SHA2_512",
                algorithm_id=7,
                key_size=64,
                hash_algorithm_name="SHA512"
            ),
        })

    def encrypt(
        self,
        algorithm: str,
        key: bytes,
        plaintext: bytes,
        iv: Optional[bytes] = None,
        aad: Optional[bytes] = None
    ) -> Tuple[bytes, bytes]:
        """
        Encrypt data using specified algorithm.

        Args:
            algorithm: Encryption algorithm name
            key: Encryption key
            plaintext: Data to encrypt
            iv: Initialization vector (generated if None)
            aad: Additional authenticated data (for AEAD)

        Returns:
            Tuple of (ciphertext, iv_used)
        """
        alg = self._encryption_algs.get(algorithm)
        if not alg:
            raise ValueError(f"Unsupported encryption algorithm: {algorithm}")

        if iv is None:
            iv = secrets.token_bytes(alg.iv_size)

        return alg.encrypt(key, plaintext, iv, aad), iv

    def decrypt(
        self,
        algorithm: str,
        key: bytes,
        ciphertext: bytes,
        iv: bytes,
        aad: Optional[bytes] = None,
        tag: Optional[bytes] = None
    ) -> bytes:
        """
        Decrypt data using specified algorithm.

        Args:
            algorithm: Encryption algorithm name
            key: Decryption key
            ciphertext: Data to decrypt
            iv: Initialization vector
            aad: Additional authenticated data (for AEAD)
            tag: Authentication tag (for AEAD)

        Returns:
            Decrypted plaintext
        """
        alg = self._encryption_algs.get(algorithm)
        if not alg:
            raise ValueError(f"Unsupported encryption algorithm: {algorithm}")

        return alg.decrypt(key, ciphertext, iv, aad, tag)

    def compute_integrity(
        self,
        algorithm: str,
        key: bytes,
        data: bytes
    ) -> bytes:
        """
        Compute integrity check value.

        Args:
            algorithm: Integrity algorithm name
            key: Integrity key
            data: Data to protect

        Returns:
            Integrity check value
        """
        alg = self._integrity_algs.get(algorithm)
        if not alg:
            raise ValueError(f"Unsupported integrity algorithm: {algorithm}")

        return alg.compute(key, data)

    def verify_integrity(
        self,
        algorithm: str,
        key: bytes,
        data: bytes,
        expected_icv: bytes
    ) -> bool:
        """
        Verify integrity check value.

        Args:
            algorithm: Integrity algorithm name
            key: Integrity key
            data: Data to verify
            expected_icv: Expected integrity check value

        Returns:
            True if verification succeeds
        """
        computed_icv = self.compute_integrity(algorithm, key, data)
        return secrets.compare_digest(computed_icv, expected_icv)

    def derive_keys(
        self,
        prf_algorithm: str,
        shared_secret: bytes,
        nonce_i: bytes,
        nonce_r: bytes,
        spi_i: bytes,
        spi_r: bytes,
        key_lengths: Dict[str, int]
    ) -> Dict[str, bytes]:
        """
        Derive IKEv2 keys using specified PRF.

        Args:
            prf_algorithm: PRF algorithm name
            shared_secret: DH shared secret
            nonce_i: Initiator nonce
            nonce_r: Responder nonce
            spi_i: Initiator SPI
            spi_r: Responder SPI
            key_lengths: Required key lengths

        Returns:
            Dictionary of derived keys
        """
        prf = self._prf_algs.get(prf_algorithm)
        if not prf:
            raise ValueError(f"Unsupported PRF algorithm: {prf_algorithm}")

        return self.key_manager.derive_ikev2_keys(
            prf, shared_secret, nonce_i, nonce_r,
            spi_i, spi_r, key_lengths
        )

    def generate_dh_keypair(self, group: int) -> Tuple[bytes, bytes]:
        """
        Generate Diffie-Hellman key pair.

        Args:
            group: DH group number

        Returns:
            Tuple of (private_key, public_key)
        """
        return self.dh_manager.generate_keypair(group)

    def compute_dh_shared_secret(
        self,
        group: int,
        private_key: bytes,
        peer_public_key: bytes
    ) -> bytes:
        """
        Compute DH shared secret.

        Args:
            group: DH group number
            private_key: Own private key
            peer_public_key: Peer's public key

        Returns:
            Shared secret
        """
        return self.dh_manager.compute_shared_secret(
            group, private_key, peer_public_key
        )

    def get_supported_algorithms(self) -> Dict[str, List[str]]:
        """Get list of supported algorithms by type."""
        return {
            "encryption": list(self._encryption_algs.keys()),
            "integrity": list(self._integrity_algs.keys()),
            "prf": list(self._prf_algs.keys()),
            "dh_groups": self.dh_manager.get_supported_groups()
        }

"""
Cryptographic algorithm implementations.

This module contains concrete implementations of encryption, integrity,
and PRF algorithms used in IPsec.
"""

import secrets
from typing import List, Optional, Any
from dataclasses import dataclass

from cryptography.hazmat.primitives import hashes, hmac
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.ciphers.aead import AESGCM, ChaCha20Poly1305
from cryptography.hazmat.backends import default_backend


@dataclass
class EncryptionAlgorithm:
    """Base class for encryption algorithms."""

    name: str
    algorithm_id: int
    key_sizes: List[int]
    block_size: int
    iv_size: int
    is_aead: bool

    def encrypt(
        self,
        key: bytes,
        plaintext: bytes,
        iv: bytes,
        aad: Optional[bytes] = None
    ) -> bytes:
        """Encrypt plaintext."""
        if self.is_aead:
            return self._encrypt_aead(key, plaintext, iv, aad)
        else:
            return self._encrypt_traditional(key, plaintext, iv)

    def decrypt(
        self,
        key: bytes,
        ciphertext: bytes,
        iv: bytes,
        aad: Optional[bytes] = None,
        tag: Optional[bytes] = None
    ) -> bytes:
        """Decrypt ciphertext."""
        if self.is_aead:
            return self._decrypt_aead(key, ciphertext, iv, aad, tag)
        else:
            return self._decrypt_traditional(key, ciphertext, iv)

    def _encrypt_aead(
        self,
        key: bytes,
        plaintext: bytes,
        iv: bytes,
        aad: Optional[bytes] = None
    ) -> bytes:
        """Encrypt using AEAD algorithm."""
        if self.name == "AES_GCM_16":
            aesgcm = AESGCM(key)
            return aesgcm.encrypt(iv, plaintext, aad)
        elif self.name == "CHACHA20_POLY1305":
            chacha = ChaCha20Poly1305(key)
            return chacha.encrypt(iv, plaintext, aad)
        else:
            raise ValueError(f"Unsupported AEAD algorithm: {self.name}")

    def _decrypt_aead(
        self,
        key: bytes,
        ciphertext: bytes,
        iv: bytes,
        aad: Optional[bytes] = None,
        tag: Optional[bytes] = None
    ) -> bytes:
        """Decrypt using AEAD algorithm."""
        if self.name == "AES_GCM_16":
            aesgcm = AESGCM(key)
            return aesgcm.decrypt(iv, ciphertext, aad)
        elif self.name == "CHACHA20_POLY1305":
            chacha = ChaCha20Poly1305(key)
            return chacha.decrypt(iv, ciphertext, aad)
        else:
            raise ValueError(f"Unsupported AEAD algorithm: {self.name}")

    def _encrypt_traditional(
        self,
        key: bytes,
        plaintext: bytes,
        iv: bytes
    ) -> bytes:
        """Encrypt using traditional (non-AEAD) algorithm."""
        if self.name == "AES_CBC":
            cipher = Cipher(
                algorithms.AES(key),
                modes.CBC(iv),
                backend=default_backend()
            )
            encryptor = cipher.encryptor()

            # Add PKCS7 padding
            pad_len = self.block_size - (len(plaintext) % self.block_size)
            padded = plaintext + bytes([pad_len] * pad_len)

            return encryptor.update(padded) + encryptor.finalize()
        else:
            raise ValueError(f"Unsupported traditional algorithm: {self.name}")

    def _decrypt_traditional(
        self,
        key: bytes,
        ciphertext: bytes,
        iv: bytes
    ) -> bytes:
        """Decrypt using traditional (non-AEAD) algorithm."""
        if self.name == "AES_CBC":
            cipher = Cipher(
                algorithms.AES(key),
                modes.CBC(iv),
                backend=default_backend()
            )
            decryptor = cipher.decryptor()
            padded = decryptor.update(ciphertext) + decryptor.finalize()

            # Remove PKCS7 padding
            pad_len = padded[-1]
            return padded[:-pad_len]
        else:
            raise ValueError(f"Unsupported traditional algorithm: {self.name}")


@dataclass
class IntegrityAlgorithm:
    """Base class for integrity algorithms."""

    name: str
    algorithm_id: int
    key_size: int
    hash_size: int
    hash_algorithm_name: str

    @property
    def hash_function(self) -> Any:
        """Get the cryptography hash function object."""
        hash_map = {
            "SHA256": hashes.SHA256(),
            "SHA384": hashes.SHA384(),
            "SHA512": hashes.SHA512(),
        }
        return hash_map.get(self.hash_algorithm_name)

    def compute(self, key: bytes, data: bytes) -> bytes:
        """Compute integrity check value."""
        h = hmac.HMAC(key, self.hash_function, backend=default_backend())
        h.update(data)
        # Truncate to specified hash size
        return h.finalize()[:self.hash_size]

    def verify(self, key: bytes, data: bytes, expected_icv: bytes) -> bool:
        """Verify integrity check value."""
        computed_icv = self.compute(key, data)
        return secrets.compare_digest(computed_icv, expected_icv)


@dataclass
class PRFAlgorithm:
    """Base class for PRF algorithms."""

    name: str
    algorithm_id: int
    key_size: int
    hash_algorithm_name: str

    @property
    def hash_function(self) -> Any:
        """Get the cryptography hash function object."""
        hash_map = {
            "SHA256": hashes.SHA256(),
            "SHA384": hashes.SHA384(),
            "SHA512": hashes.SHA512(),
        }
        return hash_map.get(self.hash_algorithm_name)

    def compute(self, key: bytes, data: bytes) -> bytes:
        """Compute PRF output."""
        h = hmac.HMAC(key, self.hash_function, backend=default_backend())
        h.update(data)
        return h.finalize()

    def expand(self, key: bytes, info: bytes, length: int) -> bytes:
        """Expand key material using PRF."""
        output = b""
        counter = 1

        while len(output) < length:
            h = hmac.HMAC(key, self.hash_function, backend=default_backend())
            h.update(info + counter.to_bytes(1, 'big'))
            output += h.finalize()
            counter += 1

        return output[:length]

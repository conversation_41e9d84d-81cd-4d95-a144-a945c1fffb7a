"""
Enhanced IKEv2 protocol implementation with comprehensive hook support.

This module provides a modern, extensible IKEv2 implementation that improves upon
the original ipsecdr design with better separation of concerns, comprehensive
hook system, and enhanced error handling.
"""

import asyncio
import secrets
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Callable, Any, Union
from dataclasses import dataclass, field

from scapy.contrib.ikev2 import IKEv2
from scapy.layers.inet import IP, UDP

from ...models.base import BaseModel, TimestampedModel
from ...utils.logging import get_logger
from ..crypto.algorithms import EncryptionAlgorithm, IntegrityAlgorithm, PRFAlgorithm
from .payloads import IKEv2PayloadBuilder
from .state import IKEv2StateMachine, IKEv2State, ExchangeType
from .hooks import IKEv2HookManager, HookType, HookContext

logger = get_logger(__name__)


class IKEv2Role(Enum):
    """IKEv2 peer role."""
    INITIATOR = "initiator"
    RESPONDER = "responder"


class IKEv2Version(Enum):
    """IKEv2 protocol versions."""
    V2 = 0x20


@dataclass
class IKEv2SecurityAssociation:
    """IKEv2 Security Association data."""
    
    spi_i: bytes
    spi_r: bytes
    encryption_algorithm: EncryptionAlgorithm
    integrity_algorithm: Optional[IntegrityAlgorithm]
    prf_algorithm: PRFAlgorithm
    dh_group: int
    
    # Key material
    sk_d: Optional[bytes] = None
    sk_ai: Optional[bytes] = None
    sk_ar: Optional[bytes] = None
    sk_ei: Optional[bytes] = None
    sk_er: Optional[bytes] = None
    sk_pi: Optional[bytes] = None
    sk_pr: Optional[bytes] = None
    
    # State tracking
    created_at: datetime = field(default_factory=datetime.now)
    last_used: datetime = field(default_factory=datetime.now)
    message_id: int = 0
    
    def update_last_used(self):
        """Update the last used timestamp."""
        self.last_used = datetime.now()
    
    def increment_message_id(self) -> int:
        """Increment and return the message ID."""
        self.message_id += 1
        return self.message_id


@dataclass
class IKEv2Exchange:
    """Represents an IKEv2 exchange with metadata."""
    
    exchange_type: ExchangeType
    message_id: int
    initiator_packet: Optional[IKEv2] = None
    responder_packet: Optional[IKEv2] = None
    started_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    success: bool = False
    error_message: Optional[str] = None
    
    # Hook execution results
    hook_results: Dict[str, Any] = field(default_factory=dict)
    
    def mark_completed(self, success: bool = True, error: Optional[str] = None):
        """Mark the exchange as completed."""
        self.completed_at = datetime.now()
        self.success = success
        self.error_message = error


class IKEv2Protocol:
    """
    Enhanced IKEv2 protocol implementation with comprehensive hook support.
    
    This class provides a complete IKEv2 implementation that supports:
    - Comprehensive hook system for packet and exchange callbacks
    - State machine management
    - Security association management
    - Extensible payload handling
    - Rich metadata collection
    """
    
    def __init__(
        self,
        role: IKEv2Role,
        local_ip: str,
        remote_ip: str,
        local_port: int = 500,
        remote_port: int = 500,
        hook_manager: Optional[IKEv2HookManager] = None
    ):
        """
        Initialize the IKEv2 protocol handler.
        
        Args:
            role: The role of this peer (initiator or responder)
            local_ip: Local IP address
            remote_ip: Remote IP address
            local_port: Local port (default 500)
            remote_port: Remote port (default 500)
            hook_manager: Optional hook manager for callbacks
        """
        self.role = role
        self.local_ip = local_ip
        self.remote_ip = remote_ip
        self.local_port = local_port
        self.remote_port = remote_port
        
        # Core components
        self.state_machine = IKEv2StateMachine(role)
        self.hook_manager = hook_manager or IKEv2HookManager()
        self.payload_builder = IKEv2PayloadBuilder()
        
        # Security associations
        self.ike_sa: Optional[IKEv2SecurityAssociation] = None
        self.child_sas: Dict[bytes, Any] = {}  # SPI -> Child SA
        
        # Exchange tracking
        self.exchanges: List[IKEv2Exchange] = []
        self.current_exchange: Optional[IKEv2Exchange] = None
        
        # Packet tracking
        self.sent_packets: List[IKEv2] = []
        self.received_packets: List[IKEv2] = []
        self.packet_count = 0
        
        # Configuration
        self.nat_traversal = False
        self.cookie_required = False
        
        logger.info(f"IKEv2Protocol initialized as {role.value} ({local_ip}:{local_port} -> {remote_ip}:{remote_port})")
    
    async def start_exchange(
        self,
        exchange_type: ExchangeType,
        **kwargs
    ) -> IKEv2Exchange:
        """
        Start a new IKEv2 exchange.
        
        Args:
            exchange_type: Type of exchange to start
            **kwargs: Additional parameters for the exchange
            
        Returns:
            The created exchange object
        """
        logger.info(f"Starting {exchange_type.value} exchange")
        
        # Create exchange object
        message_id = self.ike_sa.increment_message_id() if self.ike_sa else 0
        exchange = IKEv2Exchange(
            exchange_type=exchange_type,
            message_id=message_id
        )
        
        self.current_exchange = exchange
        self.exchanges.append(exchange)
        
        # Execute pre-exchange hooks
        hook_context = HookContext(
            exchange_type=exchange_type,
            message_id=message_id,
            role=self.role,
            packet_number=self.packet_count + 1,
            metadata=kwargs
        )
        
        await self.hook_manager.execute_hooks(
            HookType.PRE_EXCHANGE,
            hook_context
        )
        
        # Update state machine
        self.state_machine.start_exchange(exchange_type)
        
        return exchange
    
    async def process_packet(
        self,
        packet: IKEv2,
        source_addr: tuple
    ) -> Optional[IKEv2]:
        """
        Process an incoming IKEv2 packet.
        
        Args:
            packet: The received IKEv2 packet
            source_addr: Source address tuple (ip, port)
            
        Returns:
            Response packet if one should be sent
        """
        self.packet_count += 1
        self.received_packets.append(packet)
        
        logger.debug(f"Processing packet #{self.packet_count}: {packet.summary()}")
        
        # Create hook context
        hook_context = HookContext(
            exchange_type=ExchangeType(packet.exch_type),
            message_id=packet.id,
            role=self.role,
            packet_number=self.packet_count,
            packet=packet,
            source_addr=source_addr,
            metadata={}
        )
        
        try:
            # Execute pre-packet hooks
            await self.hook_manager.execute_hooks(
                HookType.PRE_PACKET,
                hook_context
            )
            
            # Process based on exchange type
            response = await self._handle_exchange_packet(packet, hook_context)
            
            # Execute post-packet hooks
            hook_context.response_packet = response
            await self.hook_manager.execute_hooks(
                HookType.POST_PACKET,
                hook_context
            )
            
            # Track sent packet
            if response:
                self.sent_packets.append(response)
                logger.debug(f"Generated response: {response.summary()}")
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing packet: {e}")
            
            # Execute error hooks
            hook_context.error = e
            await self.hook_manager.execute_hooks(
                HookType.ERROR,
                hook_context
            )
            
            raise
    
    async def _handle_exchange_packet(
        self,
        packet: IKEv2,
        context: HookContext
    ) -> Optional[IKEv2]:
        """
        Handle a packet based on its exchange type.
        
        Args:
            packet: The IKEv2 packet to handle
            context: Hook context for the packet
            
        Returns:
            Response packet if one should be generated
        """
        exchange_type = ExchangeType(packet.exch_type)
        
        # Route to appropriate handler
        if exchange_type == ExchangeType.IKE_SA_INIT:
            return await self._handle_ike_sa_init(packet, context)
        elif exchange_type == ExchangeType.IKE_AUTH:
            return await self._handle_ike_auth(packet, context)
        elif exchange_type == ExchangeType.CREATE_CHILD_SA:
            return await self._handle_create_child_sa(packet, context)
        elif exchange_type == ExchangeType.INFORMATIONAL:
            return await self._handle_informational(packet, context)
        else:
            logger.warning(f"Unsupported exchange type: {exchange_type}")
            return None
    
    async def _handle_ike_sa_init(
        self,
        packet: IKEv2,
        context: HookContext
    ) -> Optional[IKEv2]:
        """Handle IKE_SA_INIT exchange."""
        logger.debug("Handling IKE_SA_INIT exchange")
        
        # Execute exchange-specific hooks
        await self.hook_manager.execute_hooks(
            HookType.EXCHANGE_IKE_SA_INIT,
            context
        )
        
        if self.role == IKEv2Role.RESPONDER:
            # Process initiator's proposal
            # TODO: Implement proposal processing
            
            # Generate response
            response = await self._generate_ike_sa_init_response(packet, context)
            
            # Update state
            self.state_machine.transition_to(IKEv2State.SA_INIT_SENT)
            
            return response
        else:
            # Process responder's response
            # TODO: Implement response processing
            
            # Update state
            self.state_machine.transition_to(IKEv2State.SA_INIT_RECEIVED)
            
            return None
    
    async def _handle_ike_auth(
        self,
        packet: IKEv2,
        context: HookContext
    ) -> Optional[IKEv2]:
        """Handle IKE_AUTH exchange."""
        logger.debug("Handling IKE_AUTH exchange")
        
        # Execute exchange-specific hooks
        await self.hook_manager.execute_hooks(
            HookType.EXCHANGE_IKE_AUTH,
            context
        )
        
        # TODO: Implement IKE_AUTH handling
        return None
    
    async def _handle_create_child_sa(
        self,
        packet: IKEv2,
        context: HookContext
    ) -> Optional[IKEv2]:
        """Handle CREATE_CHILD_SA exchange."""
        logger.debug("Handling CREATE_CHILD_SA exchange")
        
        # Execute exchange-specific hooks
        await self.hook_manager.execute_hooks(
            HookType.EXCHANGE_CREATE_CHILD_SA,
            context
        )
        
        # TODO: Implement CREATE_CHILD_SA handling
        return None
    
    async def _handle_informational(
        self,
        packet: IKEv2,
        context: HookContext
    ) -> Optional[IKEv2]:
        """Handle INFORMATIONAL exchange."""
        logger.debug("Handling INFORMATIONAL exchange")
        
        # Execute exchange-specific hooks
        await self.hook_manager.execute_hooks(
            HookType.EXCHANGE_INFORMATIONAL,
            context
        )
        
        # TODO: Implement INFORMATIONAL handling
        return None
    
    async def _generate_ike_sa_init_response(
        self,
        request: IKEv2,
        context: HookContext
    ) -> IKEv2:
        """Generate IKE_SA_INIT response packet."""
        # TODO: Implement response generation
        # This is a placeholder that will be expanded
        
        response = IKEv2(
            init_SPI=request.init_SPI,
            resp_SPI=secrets.token_bytes(8),
            next_payload="SA",
            exch_type="IKE_SA_INIT",
            flags="Response",
            id=request.id
        )
        
        return response
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get protocol statistics."""
        return {
            "role": self.role.value,
            "state": self.state_machine.current_state.value,
            "packets_sent": len(self.sent_packets),
            "packets_received": len(self.received_packets),
            "exchanges_completed": len([e for e in self.exchanges if e.completed_at]),
            "exchanges_failed": len([e for e in self.exchanges if not e.success and e.completed_at]),
            "ike_sa_established": self.ike_sa is not None,
            "child_sas_count": len(self.child_sas),
            "hook_executions": self.hook_manager.get_execution_count()
        }

"""
Configuration models for IPsec Evaluator.
"""

import yaml
from pathlib import Path
from typing import Dict, List, Optional, Union, Any

from pydantic import Field, validator, model_validator

from .base import ConfigModel


class GlobalConfig(ConfigModel):
    """Global application configuration."""
    
    timeout: int = Field(30, description="Default timeout in seconds", ge=1)
    verbose: bool = Field(False, description="Enable verbose logging")
    log_level: str = Field("INFO", description="Logging level")
    log_file: Optional[Path] = Field(None, description="Log file path")
    max_concurrent_tests: int = Field(5, description="Maximum concurrent tests", ge=1)
    
    # Rich logging configuration
    rich_tracebacks: bool = Field(True, description="Enable Rich traceback formatting")
    show_locals: bool = Field(False, description="Show local variables in tracebacks")
    
    @validator("log_level")
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of {valid_levels}")
        return v.upper()


class NetworkConfig(ConfigModel):
    """Network configuration for IPsec testing."""
    
    # Interface configuration
    interface: str = Field("eth0", description="Network interface")
    
    # IP addresses
    initiator_ip: str = Field("************", description="Initiator IP address")
    responder_ip: str = Field("************", description="Responder IP address")
    
    # Ports
    ike_port: int = Field(500, description="IKE port", ge=1, le=65535)
    nat_t_port: int = Field(4500, description="NAT-T port", ge=1, le=65535)
    
    # NAT traversal
    nat_traversal: bool = Field(True, description="Enable NAT traversal")
    
    # Network ranges
    test_network: str = Field("*************/24", description="Test network CIDR")
    
    @validator("initiator_ip", "responder_ip")
    def validate_ip(cls, v):
        import ipaddress
        try:
            ipaddress.ip_address(v)
        except ValueError:
            raise ValueError(f"Invalid IP address: {v}")
        return v


class CryptoConfig(ConfigModel):
    """Cryptographic configuration."""
    
    # IKE SA algorithms
    ike_encryption: List[str] = Field(
        ["AES_GCM_16"], 
        description="IKE encryption algorithms"
    )
    ike_integrity: List[str] = Field(
        ["HMAC_SHA2_256"], 
        description="IKE integrity algorithms"
    )
    ike_prf: List[str] = Field(
        ["PRF_HMAC_SHA2_256"], 
        description="IKE PRF algorithms"
    )
    ike_dh_groups: List[int] = Field(
        [19], 
        description="IKE DH groups"
    )
    
    # Child SA algorithms
    esp_encryption: List[str] = Field(
        ["AES_GCM_16"], 
        description="ESP encryption algorithms"
    )
    esp_integrity: List[str] = Field(
        ["HMAC_SHA2_256"], 
        description="ESP integrity algorithms"
    )
    
    # Key sizes
    encryption_key_sizes: List[int] = Field(
        [256], 
        description="Encryption key sizes in bits"
    )
    
    # Certificate configuration
    ca_cert_path: Optional[Path] = Field(None, description="CA certificate path")
    cert_path: Optional[Path] = Field(None, description="Entity certificate path")
    key_path: Optional[Path] = Field(None, description="Private key path")


class ReportConfig(ConfigModel):
    """Report generation configuration."""
    
    # Output formats
    default_format: str = Field("markdown", description="Default export format")
    supported_formats: List[str] = Field(
        ["markdown", "latex", "docx", "html", "pdf"],
        description="Supported export formats"
    )
    
    # Templates
    template_dir: Optional[Path] = Field(None, description="Custom template directory")
    
    # Pandoc configuration
    pandoc_options: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional pandoc options"
    )
    
    # Report content
    include_raw_data: bool = Field(False, description="Include raw test data in reports")
    include_packet_details: bool = Field(True, description="Include packet analysis details")
    include_compliance_matrix: bool = Field(True, description="Include compliance matrix")


class TestConfiguration(ConfigModel):
    """Complete test configuration combining all config sections."""
    
    global_config: GlobalConfig = Field(default_factory=GlobalConfig)
    network: NetworkConfig = Field(default_factory=NetworkConfig)
    crypto: CryptoConfig = Field(default_factory=CryptoConfig)
    report: ReportConfig = Field(default_factory=ReportConfig)
    
    # Directories
    test_data_dir: Path = Field(Path("./test_data"), description="Test data directory")
    results_dir: Path = Field(Path("./results"), description="Results directory")
    scenarios_dir: Path = Field(Path("./scenarios"), description="Scenarios directory")
    configs_dir: Path = Field(Path("./configs"), description="Configuration directory")
    
    @validator("test_data_dir", "results_dir", "scenarios_dir", "configs_dir")
    def ensure_path_exists(cls, v):
        if isinstance(v, str):
            v = Path(v)
        v.mkdir(parents=True, exist_ok=True)
        return v
    
    @classmethod
    def from_file(cls, config_path: Path) -> "TestConfiguration":
        """Load configuration from YAML file."""
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        with open(config_path, 'r') as f:
            data = yaml.safe_load(f)
        
        return cls(**data)
    
    def save_to_file(self, config_path: Path) -> None:
        """Save configuration to YAML file."""
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_path, 'w') as f:
            yaml.dump(
                self.dict(exclude_none=True), 
                f, 
                default_flow_style=False,
                sort_keys=False
            )
    
    def merge_cli_options(self, **kwargs) -> "TestConfiguration":
        """Merge CLI options into configuration."""
        config_dict = self.dict()
        
        # Map CLI options to config structure
        cli_mappings = {
            'verbose': ('global_config', 'verbose'),
            'log_level': ('global_config', 'log_level'),
            'log_file': ('global_config', 'log_file'),
            'max_concurrent': ('global_config', 'max_concurrent_tests'),
            'output_dir': ('results_dir',),
            'scenarios_dir': ('scenarios_dir',),
        }
        
        for cli_key, config_path in cli_mappings.items():
            if cli_key in kwargs and kwargs[cli_key] is not None:
                if len(config_path) == 1:
                    config_dict[config_path[0]] = kwargs[cli_key]
                else:
                    if config_path[0] not in config_dict:
                        config_dict[config_path[0]] = {}
                    config_dict[config_path[0]][config_path[1]] = kwargs[cli_key]
        
        return self.__class__(**config_dict)
    
    def get_scenario_path(self, scenario_name: str) -> Path:
        """Get the full path to a scenario file."""
        for ext in ['.yml', '.yaml']:
            scenario_path = self.scenarios_dir / f"{scenario_name}{ext}"
            if scenario_path.exists():
                return scenario_path
        
        raise FileNotFoundError(f"Scenario file not found: {scenario_name}")
    
    def get_result_path(self, test_id: str, format: str = "json") -> Path:
        """Get the path for a test result file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{test_id}_{timestamp}.{format}"
        return self.results_dir / filename
    
    def validate_crypto_config(self) -> List[str]:
        """Validate cryptographic configuration against ANSSI requirements."""
        issues = []
        
        # Check for ANSSI-approved algorithms
        approved_encryption = ["AES_GCM_16", "AES_CBC", "CHACHA20_POLY1305"]
        for alg in self.crypto.ike_encryption:
            if alg not in approved_encryption:
                issues.append(f"Non-ANSSI encryption algorithm: {alg}")
        
        approved_integrity = ["HMAC_SHA2_256", "HMAC_SHA2_384", "HMAC_SHA2_512"]
        for alg in self.crypto.ike_integrity:
            if alg not in approved_integrity:
                issues.append(f"Non-ANSSI integrity algorithm: {alg}")
        
        # Check key sizes
        for size in self.crypto.encryption_key_sizes:
            if size < 256:
                issues.append(f"Insufficient key size: {size} bits (minimum 256)")
        
        # Check DH groups
        approved_dh_groups = [14, 15, 16, 17, 18, 19, 20, 21, 31]
        for group in self.crypto.ike_dh_groups:
            if group not in approved_dh_groups:
                issues.append(f"Non-ANSSI DH group: {group}")
        
        return issues


# Import datetime for get_result_path method
from datetime import datetime

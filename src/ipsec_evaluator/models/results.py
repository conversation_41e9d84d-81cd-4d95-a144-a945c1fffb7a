"""
Test results and compliance reporting models.
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from pydantic import Field

from .base import BaseModel, IdentifiedModel, TimestampedModel, ComplianceLevel


class PacketAnalysis(BaseModel):
    """Analysis of a single packet."""
    
    packet_number: int = Field(..., description="Packet sequence number")
    exchange_type: str = Field(..., description="Exchange type")
    timestamp: datetime = Field(..., description="Packet timestamp")
    size: int = Field(..., description="Packet size in bytes")
    source_ip: str = Field(..., description="Source IP address")
    destination_ip: str = Field(..., description="Destination IP address")
    
    # Cryptographic analysis
    algorithms_used: Dict[str, str] = Field(default_factory=dict)
    key_sizes: Dict[str, int] = Field(default_factory=dict)
    
    # Compliance analysis
    compliance_issues: List[str] = Field(default_factory=list)
    anssi_compliant: bool = Field(True, description="ANSSI compliance status")
    
    # Raw data
    raw_data: Optional[bytes] = Field(None, description="Raw packet data")
    parsed_data: Dict[str, Any] = Field(default_factory=dict)


class ExchangeAnalysis(BaseModel):
    """Analysis of a complete exchange."""
    
    exchange_type: str = Field(..., description="Exchange type")
    start_time: datetime = Field(..., description="Exchange start time")
    end_time: Optional[datetime] = Field(None, description="Exchange end time")
    duration: Optional[float] = Field(None, description="Exchange duration in seconds")
    
    packets: List[PacketAnalysis] = Field(default_factory=list)
    
    # Exchange-level analysis
    successful: bool = Field(True, description="Exchange success status")
    algorithms_negotiated: Dict[str, str] = Field(default_factory=dict)
    security_associations: List[Dict[str, Any]] = Field(default_factory=list)
    
    # Compliance
    compliance_level: ComplianceLevel = Field(ComplianceLevel.UNKNOWN)
    compliance_details: Dict[str, Any] = Field(default_factory=dict)
    
    def add_packet(self, packet: PacketAnalysis) -> None:
        """Add packet analysis to the exchange."""
        self.packets.append(packet)
        
        # Update end time
        if self.end_time is None or packet.timestamp > self.end_time:
            self.end_time = packet.timestamp
            
        # Calculate duration
        if self.end_time:
            self.duration = (self.end_time - self.start_time).total_seconds()


class HookResult(BaseModel):
    """Result from a hook execution."""
    
    hook_name: str = Field(..., description="Hook function name")
    hook_type: str = Field(..., description="Hook type")
    execution_time: datetime = Field(..., description="Hook execution time")
    success: bool = Field(True, description="Hook execution success")
    
    # Hook data
    input_data: Dict[str, Any] = Field(default_factory=dict)
    output_data: Dict[str, Any] = Field(default_factory=dict)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    # Error information
    error_message: Optional[str] = Field(None, description="Error message if failed")
    stack_trace: Optional[str] = Field(None, description="Stack trace if failed")


class ComplianceResult(BaseModel):
    """Result of compliance checking."""
    
    level: ComplianceLevel = Field(..., description="Overall compliance level")
    score: float = Field(0.0, description="Compliance score (0-100)", ge=0, le=100)
    
    # Detailed results
    algorithm_compliance: Dict[str, bool] = Field(default_factory=dict)
    key_size_compliance: Dict[str, bool] = Field(default_factory=dict)
    protocol_compliance: Dict[str, bool] = Field(default_factory=dict)
    
    # Issues and recommendations
    issues: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    recommendations: List[str] = Field(default_factory=list)
    
    # ANSSI-specific checks
    anssi_checks: Dict[str, bool] = Field(default_factory=dict)
    anssi_score: float = Field(0.0, description="ANSSI compliance score", ge=0, le=100)


class TestData(BaseModel):
    """Raw test execution data."""
    
    exchanges: List[ExchangeAnalysis] = Field(default_factory=list)
    hook_results: List[HookResult] = Field(default_factory=list)
    
    # Infrastructure data
    container_logs: Dict[str, str] = Field(default_factory=dict)
    network_captures: List[str] = Field(default_factory=list)
    
    # Configuration used
    test_configuration: Dict[str, Any] = Field(default_factory=dict)
    
    def add_exchange(self, exchange: ExchangeAnalysis) -> None:
        """Add exchange analysis to test data."""
        self.exchanges.append(exchange)
    
    def add_hook_result(self, hook_result: HookResult) -> None:
        """Add hook result to test data."""
        self.hook_results.append(hook_result)


class TestResult(IdentifiedModel, TimestampedModel):
    """Complete test execution result."""
    
    test_id: str = Field(..., description="Unique test identifier")
    scenario_name: str = Field(..., description="Test scenario name")
    mode: str = Field(..., description="Test mode (initiator/responder)")
    
    # Execution status
    status: str = Field(..., description="Test execution status")
    start_time: datetime = Field(..., description="Test start time")
    end_time: Optional[datetime] = Field(None, description="Test end time")
    duration: float = Field(0.0, description="Test duration in seconds")
    
    # Results
    compliance: str = Field(..., description="Compliance level")
    test_data: Optional[TestData] = Field(None, description="Raw test data")
    compliance_details: Optional[ComplianceResult] = Field(None, description="Detailed compliance results")
    
    # Error handling
    error_message: Optional[str] = Field(None, description="Error message if failed")
    stack_trace: Optional[str] = Field(None, description="Stack trace if failed")
    
    def save_to_file(self, file_path: Path) -> None:
        """Save test result to JSON file."""
        with open(file_path, 'w') as f:
            json.dump(self.dict(), f, indent=2, default=str)
    
    @classmethod
    def from_file(cls, file_path: Path) -> "TestResult":
        """Load test result from JSON file."""
        with open(file_path, 'r') as f:
            data = json.load(f)
        return cls(**data)


class ComplianceReport(TimestampedModel):
    """Comprehensive compliance report."""
    
    generated_at: datetime = Field(..., description="Report generation time")
    
    # Summary statistics
    total_tests: int = Field(0, description="Total number of tests")
    passed_tests: int = Field(0, description="Number of passed tests")
    failed_tests: int = Field(0, description="Number of failed tests")
    
    # Compliance statistics
    compliant_tests: int = Field(0, description="Number of compliant tests")
    non_compliant_tests: int = Field(0, description="Number of non-compliant tests")
    partial_compliant_tests: int = Field(0, description="Number of partially compliant tests")
    
    # Overall scores
    overall_compliance_score: float = Field(0.0, description="Overall compliance score", ge=0, le=100)
    anssi_compliance_score: float = Field(0.0, description="ANSSI compliance score", ge=0, le=100)
    
    # Detailed results
    results: List[TestResult] = Field(default_factory=list)
    
    # Analysis
    common_issues: List[str] = Field(default_factory=list)
    recommendations: List[str] = Field(default_factory=list)
    
    def save_to_file(self, file_path: Path) -> None:
        """Save compliance report to JSON file."""
        with open(file_path, 'w') as f:
            json.dump(self.dict(), f, indent=2, default=str)
    
    @classmethod
    def from_file(cls, file_path: Path) -> "ComplianceReport":
        """Load compliance report from JSON file."""
        with open(file_path, 'r') as f:
            data = json.load(f)
        return cls(**data)
    
    def calculate_statistics(self) -> None:
        """Calculate report statistics from results."""
        self.total_tests = len(self.results)
        self.passed_tests = len([r for r in self.results if r.status == "completed"])
        self.failed_tests = len([r for r in self.results if r.status == "failed"])
        
        self.compliant_tests = len([r for r in self.results if r.compliance == "compliant"])
        self.non_compliant_tests = len([r for r in self.results if r.compliance == "non_compliant"])
        self.partial_compliant_tests = len([r for r in self.results if r.compliance == "partial"])
        
        # Calculate overall scores
        if self.total_tests > 0:
            self.overall_compliance_score = (self.compliant_tests / self.total_tests) * 100
            
            # Calculate ANSSI score based on compliance details
            anssi_scores = []
            for result in self.results:
                if result.compliance_details and result.compliance_details.anssi_score:
                    anssi_scores.append(result.compliance_details.anssi_score)
            
            if anssi_scores:
                self.anssi_compliance_score = sum(anssi_scores) / len(anssi_scores)

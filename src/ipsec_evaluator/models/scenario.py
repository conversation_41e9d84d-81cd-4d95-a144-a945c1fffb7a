"""
Test scenario models and definitions.
"""

from pathlib import Path
from typing import Dict, List, Optional, Any
import yaml

from pydantic import Field, validator

from .base import BaseModel, IdentifiedModel, ExchangeType, TestMode


class HookDefinition(BaseModel):
    """Definition of a hook callback."""
    
    packet: Optional[int] = Field(None, description="Packet number to hook")
    exchange: Optional[ExchangeType] = Field(None, description="Exchange type to hook")
    callback: str = Field(..., description="Callback function name")
    description: Optional[str] = Field(None, description="Hook description")


class HookConfiguration(BaseModel):
    """Hook configuration for a test scenario."""
    
    packet_callbacks: List[HookDefinition] = Field(default_factory=list)
    exchange_callbacks: List[HookDefinition] = Field(default_factory=list)
    universal_callback: Optional[str] = Field(None, description="Universal callback function")


class ExchangeDefinition(BaseModel):
    """Definition of an IPsec exchange."""
    
    type: ExchangeType = Field(..., description="Exchange type")
    expected_packets: int = Field(2, description="Expected number of packets")
    timeout: int = Field(30, description="Timeout in seconds")


class CryptoConfiguration(BaseModel):
    """Cryptographic configuration for a test."""
    
    ike_encryption: List[str] = Field(default_factory=lambda: ["AES_GCM_16"])
    ike_integrity: List[str] = Field(default_factory=lambda: ["HMAC_SHA2_256"])
    ike_prf: List[str] = Field(default_factory=lambda: ["PRF_HMAC_SHA2_256"])
    ike_dh_groups: List[int] = Field(default_factory=lambda: [19])
    esp_encryption: List[str] = Field(default_factory=lambda: ["AES_GCM_16"])
    esp_integrity: List[str] = Field(default_factory=lambda: ["HMAC_SHA2_256"])


class NetworkConfiguration(BaseModel):
    """Network configuration for a test."""
    
    initiator_ip: str = Field("************", description="Initiator IP address")
    responder_ip: str = Field("************", description="Responder IP address")
    nat_traversal: bool = Field(True, description="Enable NAT traversal")


class TestConfiguration(BaseModel):
    """Configuration for a specific test."""
    
    crypto: CryptoConfiguration = Field(default_factory=CryptoConfiguration)
    network: NetworkConfiguration = Field(default_factory=NetworkConfiguration)


class ComplianceCheck(BaseModel):
    """Definition of a compliance check."""
    
    name: str = Field(..., description="Check name")
    expected: bool = Field(True, description="Expected result")


class ExpectedOutcome(BaseModel):
    """Expected outcome for an exchange."""
    
    exchange: ExchangeType = Field(..., description="Exchange type")
    status: str = Field("success", description="Expected status")
    compliance: str = Field("compliant", description="Expected compliance level")
    checks: Dict[str, Any] = Field(default_factory=dict, description="Specific checks")


class TestScenarioDefinition(BaseModel):
    """Definition of a single test scenario."""
    
    description: str = Field(..., description="Scenario description")
    mode: str = Field("strict", description="Test mode")
    hooks: HookConfiguration = Field(default_factory=HookConfiguration)
    configuration: TestConfiguration = Field(default_factory=TestConfiguration)
    exchanges: List[ExchangeDefinition] = Field(default_factory=list)
    compliance_checks: List[str] = Field(default_factory=list)


class TestScenarios(BaseModel):
    """Collection of test scenarios for initiator and responder."""
    
    initiator: Dict[str, TestScenarioDefinition] = Field(default_factory=dict)
    responder: Dict[str, TestScenarioDefinition] = Field(default_factory=dict)


class TestMetadata(BaseModel):
    """Metadata for a test scenario."""
    
    tags: List[str] = Field(default_factory=list)
    priority: str = Field("medium", description="Test priority")
    estimated_duration: int = Field(60, description="Estimated duration in seconds")
    requirements: Dict[str, Any] = Field(default_factory=dict)


class TestScenario(IdentifiedModel):
    """Complete test scenario definition."""
    
    name: str = Field(..., description="Scenario name")
    description: str = Field(..., description="Scenario description")
    version: str = Field("1.0", description="Scenario version")
    anssi_requirement: Optional[str] = Field(None, description="ANSSI requirement reference")
    
    test_scenarios: TestScenarios = Field(default_factory=TestScenarios)
    expected_outcomes: Dict[str, List[ExpectedOutcome]] = Field(default_factory=dict)
    metadata: TestMetadata = Field(default_factory=TestMetadata)
    
    @classmethod
    def from_file(cls, file_path: Path) -> "TestScenario":
        """Load test scenario from YAML file."""
        with open(file_path, 'r') as f:
            data = yaml.safe_load(f)
        return cls(**data)
    
    def save_to_file(self, file_path: Path) -> None:
        """Save test scenario to YAML file."""
        with open(file_path, 'w') as f:
            yaml.dump(self.dict(), f, default_flow_style=False)
    
    def has_initiator_tests(self) -> bool:
        """Check if scenario has initiator tests."""
        return len(self.test_scenarios.initiator) > 0
    
    def has_responder_tests(self) -> bool:
        """Check if scenario has responder tests."""
        return len(self.test_scenarios.responder) > 0
    
    def get_test_modes(self) -> List[str]:
        """Get list of available test modes."""
        modes = []
        if self.has_initiator_tests():
            modes.append("initiator")
        if self.has_responder_tests():
            modes.append("responder")
        return modes
    
    def get_scenario_for_mode(self, mode: TestMode, scenario_name: str) -> Optional[TestScenarioDefinition]:
        """Get specific scenario definition for a mode."""
        if mode == TestMode.INITIATOR:
            return self.test_scenarios.initiator.get(scenario_name)
        elif mode == TestMode.RESPONDER:
            return self.test_scenarios.responder.get(scenario_name)
        return None

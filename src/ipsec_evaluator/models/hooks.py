"""
Hook system models and definitions.
"""

from typing import Any, Callable, Dict, List, Optional
from datetime import datetime

from pydantic import Field

from .base import BaseModel, HookType


class CallbackConfig(BaseModel):
    """Configuration for a callback function."""
    
    name: str = Field(..., description="Callback function name")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Callback parameters")
    timeout: int = Field(30, description="Callback timeout in seconds")
    retry_count: int = Field(0, description="Number of retries on failure")


class HookDefinition(BaseModel):
    """Definition of a hook point."""
    
    hook_type: HookType = Field(..., description="Type of hook")
    trigger_condition: Dict[str, Any] = Field(..., description="Conditions that trigger the hook")
    callback: CallbackConfig = Field(..., description="Callback configuration")
    enabled: bool = Field(True, description="Whether the hook is enabled")
    priority: int = Field(0, description="Hook execution priority")


class HookResult(BaseModel):
    """Result of hook execution."""
    
    hook_id: str = Field(..., description="Hook identifier")
    execution_time: datetime = Field(..., description="When the hook was executed")
    success: bool = Field(..., description="Whether execution was successful")
    duration: float = Field(..., description="Execution duration in seconds")
    
    # Data
    input_data: Dict[str, Any] = Field(default_factory=dict)
    output_data: Dict[str, Any] = Field(default_factory=dict)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    # Error information
    error_message: Optional[str] = Field(None, description="Error message if failed")
    stack_trace: Optional[str] = Field(None, description="Stack trace if failed")

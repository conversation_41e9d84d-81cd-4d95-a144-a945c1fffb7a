# IPsecDR

![IPsecDR Logo](docs/ipsecdr-anssi.jpg)

## Overview

**IPsecDR** (IPsec Diffusion Restreinte) is a tool designed to facilitate the configuration, testing, and documentation of IPsec setups. It includes a suite of utilities to handle IPsec configurations, cryptographic operations, and orchestrated testing for security research and deployment.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Installation](#installation)
- [Usage](#usage)
- [Documentation](#documentation)
- [Development Practices](#development-practices)
- [Testing](#testing)
- [Contributing](#contributing)
- [License](#license)

## Features

- **IPsec Configuration Parser**: Simplifies managing complex IPsec configurations.
- **IKEv2 and ESP Management**: Handles both IKEv2 and ESP configurations with support for NAT traversal.
- **Orchestration Engine**: Runs tests in both client and server modes to simulate real-world IPsec scenarios.
- **Extensive Documentation**: Includes a full API reference and guides, built with MkDocs and MkDocsStrings.
- **Automated Testing and Formatting**: Uses `pytest`, `flake8`, `black`, and `sphinx` for consistent code quality and style.

## Installation

Ensure you have Python installed. This project is managed with [Poetry](https://python-poetry.org/) for dependency management and virtual environment handling.

1. **Clone the repository**:
    
    ```bash
    git clone https://github.com/yourusername/ipsecdr.git
    cd ipsecdr
    ```

2. **Install dependencies**:
    
    ```bash
    poetry install
    ```

3. **Install development and documentation dependencies**:
    
    ```bash
    poetry install --with dev,docs
    ```

## Usage

To start using IPsecDR, run the main entry point with the necessary command-line arguments. Below is an example of basic usage:

```bash
poetry run ipsecdr --config path/to/config.ini --client --tests T.01_ALGO_DR
```

For detailed usage instructions, see the User Guide.

For more examples, refer to the Usage Guide.

## Documentation

The full project documentation is built using MkDocs with the Material theme. You can access the documentation online or build it locally.

### Build Documentation Locally

```bash
poetry run mkdocs serve
```

Visit http://127.0.0.1:8000 in your browser to view the documentation.


## Development Practices

This project follows best practices for Python development:

    Code Formatting: We use black for consistent code formatting.
    Linting: flake8 is used to enforce code style guidelines.
    Documentation: Inline documentation follows Sphinx-style docstrings for compatibility with MkDocs integration.
    Dependencies: Managed with Poetry, ensuring consistent environments.

Refer to Practices Guide for more details.

## Testing

Testing is managed using pytest. To run the tests:

```bash
poetry run pytest
```


### Code Coverage

For code coverage, use:

```bash
poetry run pytest --cov=ipsecdr
```

Refer to the Testing Guide for more information on our testing strategies.
Contributing

We welcome contributions to IPsecDR! Please follow these guidelines:

    Fork the repository.
    Create a new branch for your feature or bugfix.
    Ensure code passes linting (flake8) and formatting (black) checks.
    Submit a pull request with a detailed description of your changes.

For more details, see CONTRIBUTING.md.

## License

This project is licensed under the OPPIDA License. See the LICENSE file for more details.

Made with ❤️ by Oppida Security Research Team.
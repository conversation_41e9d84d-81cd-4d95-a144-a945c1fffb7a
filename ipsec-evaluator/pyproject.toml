[tool.poetry]
name = "ipsec-evaluator"
version = "0.1.0"
description = "Modern IPsec compliance testing tool for ANSSI IPsecDR corpus"
authors = ["IPsec Evaluator Team <<EMAIL>>"]
readme = "README.md"
license = "MIT"
packages = [{include = "ipsec_evaluator", from = "src"}]

[tool.poetry.dependencies]
python = "^3.12"
# Core dependencies
asyncio-mqtt = "^0.16.2"
cryptography = "^43.0.1"
pydantic = "^2.9.2"
pyyaml = "^6.0.2"
rich = "^13.9.2"
scapy = "^2.6.0"
typer = "^0.12.5"
# Async support
aiofiles = "^24.1.0"
asyncio = "^3.4.3"
# Networking
netaddr = "^1.3.0"
# Configuration
toml = "^0.10.2"
# Logging
structlog = "^24.4.0"
# Testing framework
pytest-asyncio = "^0.24.0"

[tool.poetry.group.dev]
optional = true

[tool.poetry.group.dev.dependencies]
# Testing
pytest = "^8.3.3"
pytest-asyncio = "^0.24.0"
pytest-cov = "^5.0.0"
pytest-xdist = "^3.6.0"
pytest-mock = "^3.14.0"
pytest-rerunfailures = "^14.0"
pytest-randomly = "^3.15.0"
# Code quality
black = "^24.10.0"
isort = "^5.13.2"
flake8 = "^7.1.1"
mypy = "^1.11.2"
pre-commit = "^4.0.1"
# Documentation
mkdocs = "^1.6.1"
mkdocs-material = "^9.5.42"
mkdocstrings = {extras = ["python"], version = "^0.26.2"}

[tool.poetry.group.infra]
optional = true

[tool.poetry.scripts]
ipsec-evaluator = "ipsec_evaluator.cli:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

# Tool configurations
[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
    \.git
    | \.mypy_cache
    | \.venv
    | build
    | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["ipsec_evaluator"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests", 
    "e2e: End-to-end tests",
    "slow: Slow running tests",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["src"]
omit = ["*/tests/*", "*/test_*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
]

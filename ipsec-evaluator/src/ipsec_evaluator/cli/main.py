"""
Main CLI application for IPsec Evaluator.
"""

import sys
from pathlib import Path
from typing import List, Optional

import typer
from rich.console import Console
from rich.table import Table

from ..models.config import TestConfiguration
from ..engine.orchestrator import Orchestrator
from ..utils.logging import setup_logging

app = typer.Typer(
    name="ipsec-evaluator",
    help="Modern IPsec compliance testing tool for ANSSI IPsecDR corpus",
    add_completion=False,
)

console = Console()


@app.command()
def test(
    scenarios: List[str] = typer.Option(
        ..., 
        "--scenario", 
        "-s",
        help="Test scenario names to execute"
    ),
    config_file: Optional[Path] = typer.Option(
        None,
        "--config",
        "-c", 
        help="Configuration file path"
    ),
    mode: str = typer.Option(
        "both",
        "--mode",
        "-m",
        help="Test mode: initiator, responder, or both"
    ),
    verbose: bool = typer.Option(
        False,
        "--verbose",
        "-v",
        help="Enable verbose output"
    ),
    max_concurrent: int = typer.Option(
        5,
        "--max-concurrent",
        help="Maximum concurrent tests"
    ),
    output_dir: Optional[Path] = typer.Option(
        None,
        "--output",
        "-o",
        help="Output directory for results"
    ),
) -> None:
    """Execute IPsec compliance tests."""
    
    console.print(f"[bold blue]IPsec Evaluator[/bold blue] - Starting test execution")
    console.print(f"Scenarios: {', '.join(scenarios)}")
    console.print(f"Mode: {mode}")
    
    # Setup logging
    setup_logging(verbose=verbose)
    
    # Load configuration
    if config_file and config_file.exists():
        # TODO: Load from file
        config = TestConfiguration()
    else:
        config = TestConfiguration()
    
    # Update config with CLI options
    config.global_config.verbose = verbose
    config.global_config.max_concurrent_tests = max_concurrent
    
    if output_dir:
        config.results_dir = output_dir
    
    try:
        # Initialize orchestrator
        orchestrator = Orchestrator(config=config)
        
        # Execute tests
        results = orchestrator.execute_scenarios(scenarios, mode)
        
        # Display results
        _display_results(results)
        
        console.print("[bold green]Test execution completed successfully[/bold green]")
        
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        sys.exit(1)


@app.command()
def validate(
    scenario_file: Path = typer.Argument(
        ...,
        help="Scenario file to validate"
    ),
) -> None:
    """Validate a test scenario file."""
    
    console.print(f"[bold blue]Scenario Validation[/bold blue]")
    console.print(f"File: {scenario_file}")
    
    if not scenario_file.exists():
        console.print(f"[bold red]Error:[/bold red] File not found: {scenario_file}")
        sys.exit(1)
    
    try:
        from ..models.scenario import TestScenario
        
        # Load and validate scenario
        scenario = TestScenario.from_file(scenario_file)
        
        console.print(f"[bold green]✓[/bold green] Scenario '{scenario.name}' is valid")
        console.print(f"Description: {scenario.description}")
        console.print(f"Test modes: {', '.join(scenario.get_test_modes())}")
        
    except Exception as e:
        console.print(f"[bold red]✗ Validation failed:[/bold red] {e}")
        sys.exit(1)


@app.command()
def list_scenarios(
    scenarios_dir: Optional[Path] = typer.Option(
        None,
        "--dir",
        "-d",
        help="Scenarios directory path"
    ),
) -> None:
    """List available test scenarios."""
    
    if scenarios_dir is None:
        scenarios_dir = Path("scenarios")
    
    console.print(f"[bold blue]Available Test Scenarios[/bold blue]")
    console.print(f"Directory: {scenarios_dir}")
    
    if not scenarios_dir.exists():
        console.print(f"[bold red]Error:[/bold red] Directory not found: {scenarios_dir}")
        sys.exit(1)
    
    scenario_files = list(scenarios_dir.glob("*.yml")) + list(scenarios_dir.glob("*.yaml"))
    
    if not scenario_files:
        console.print("No scenario files found")
        return
    
    table = Table(title="Test Scenarios")
    table.add_column("Name", style="cyan")
    table.add_column("File", style="magenta")
    table.add_column("Description", style="green")
    
    for file_path in sorted(scenario_files):
        try:
            from ..models.scenario import TestScenario
            scenario = TestScenario.from_file(file_path)
            table.add_row(
                scenario.name,
                file_path.name,
                scenario.description or "No description"
            )
        except Exception as e:
            table.add_row(
                file_path.stem,
                file_path.name,
                f"[red]Error: {e}[/red]"
            )
    
    console.print(table)


def _display_results(results) -> None:
    """Display test results in a formatted table."""
    table = Table(title="Test Results")
    table.add_column("Scenario", style="cyan")
    table.add_column("Mode", style="magenta")
    table.add_column("Status", style="green")
    table.add_column("Compliance", style="yellow")
    table.add_column("Duration", style="blue")
    
    for result in results:
        status_style = "green" if result.status == "completed" else "red"
        compliance_style = "green" if result.compliance == "compliant" else "red"
        
        table.add_row(
            result.scenario_name,
            result.mode,
            f"[{status_style}]{result.status}[/{status_style}]",
            f"[{compliance_style}]{result.compliance}[/{compliance_style}]",
            f"{result.duration:.2f}s"
        )
    
    console.print(table)


def _display_infra_status(status) -> None:
    """Display infrastructure status."""
    table = Table(title="Infrastructure Status")
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Details", style="yellow")
    
    for component, info in status.items():
        status_style = "green" if info["status"] == "running" else "red"
        table.add_row(
            component,
            f"[{status_style}]{info['status']}[/{status_style}]",
            info.get("details", "")
        )
    
    console.print(table)


def main() -> None:
    """Main entry point for the CLI application."""
    app()


if __name__ == "__main__":
    main()

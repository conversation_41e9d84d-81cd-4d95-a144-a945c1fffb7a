
def _interactive_config_setup(template_data: Dict[str, Any]) -> Dict[str, Any]:
    """Interactive configuration setup."""
    console.print("[bold]Interactive Configuration Setup[/bold]")
    console.print("Press Enter to keep default values, or enter new values.\n")

    # Network configuration
    console.print("[bold blue]Network Configuration[/bold blue]")
    network = template_data.get('network', {})

    network['initiator_ip'] = Prompt.ask(
        "Initiator IP address",
        default=network.get('initiator_ip', '************')
    )

    network['responder_ip'] = Prompt.ask(
        "Responder IP address",
        default=network.get('responder_ip', '************')
    )

    network['ike_port'] = int(Prompt.ask(
        "IKE port",
        default=str(network.get('ike_port', 500))
    ))

    # Global configuration
    console.print("\n[bold blue]Global Configuration[/bold blue]")
    global_config = template_data.get('global_config', {})

    global_config['max_concurrent_tests'] = int(Prompt.ask(
        "Maximum concurrent tests",
        default=str(global_config.get('max_concurrent_tests', 2))
    ))

    global_config['test_timeout'] = int(Prompt.ask(
        "Test timeout (seconds)",
        default=str(global_config.get('test_timeout', 300))
    ))

    global_config['results_dir'] = Prompt.ask(
        "Results directory",
        default=global_config.get('results_dir', './test_results')
    )

    template_data['network'] = network
    template_data['global_config'] = global_config

    return template_data


def _show_config_details(parser: IPsecEvaluatorConfigParser):
    """Show detailed configuration information."""
    summary = parser.get_config_summary()

    # Global configuration
    global_panel = Panel(
        _format_config_section(summary['global_config']),
        title="[bold]Global Configuration[/bold]",
        border_style="blue"
    )
    console.print(global_panel)

    # Network configuration
    network_panel = Panel(
        _format_config_section(summary['network']),
        title="[bold]Network Configuration[/bold]",
        border_style="green"
    )
    console.print(network_panel)

    # Crypto configuration
    crypto_panel = Panel(
        _format_config_section(summary['crypto']),
        title="[bold]Crypto Configuration[/bold]",
        border_style="yellow"
    )
    console.print(crypto_panel)

    # PKI configuration (if present)
    if summary['pki']:
        pki_panel = Panel(
            _format_config_section(summary['pki']),
            title="[bold]PKI Configuration[/bold]",
            border_style="red"
        )
        console.print(pki_panel)


def _format_config_section(section_data: Dict[str, Any]) -> str:
    """Format a configuration section for display."""
    lines = []
    for key, value in section_data.items():
        if isinstance(value, list):
            value_str = ', '.join(str(v) for v in value)
        else:
            value_str = str(value)
        lines.append(f"[cyan]{key}:[/cyan] {value_str}")
    return '\n'.join(lines)


def _show_config_table(parser: IPsecEvaluatorConfigParser, section: Optional[str] = None):
    """Show configuration in table format."""
    summary = parser.get_config_summary()

    if section:
        if section in summary:
            table = Table(title=f"{section.title()} Configuration")
            table.add_column("Setting", style="cyan")
            table.add_column("Value", style="white")

            section_data = summary[section]
            for key, value in section_data.items():
                if isinstance(value, list):
                    value_str = ', '.join(str(v) for v in value)
                else:
                    value_str = str(value)
                table.add_row(key, value_str)

            console.print(table)
        else:
            console.print(f"[red]Error:[/red] Unknown section '{section}'")
            console.print(f"Available sections: {', '.join(summary.keys())}")
    else:
        # Show all sections
        for section_name, section_data in summary.items():
            if section_name in ['config_files', 'validation_enabled', 'env_prefix']:
                continue  # Skip metadata

            if section_data:  # Only show non-empty sections
                table = Table(title=f"{section_name.replace('_', ' ').title()}")
                table.add_column("Setting", style="cyan")
                table.add_column("Value", style="white")

                for key, value in section_data.items():
                    if isinstance(value, list):
                        value_str = ', '.join(str(v) for v in value)
                    else:
                        value_str = str(value)
                    table.add_row(key, value_str)

                console.print(table)
                console.print()

"""
Enhanced CLI interface for ipsec-evaluator.

This module provides a comprehensive command-line interface with:
- Configuration management commands
- Test execution commands
- Result analysis commands
- Template generation and validation
- Rich output formatting
"""

import sys
from pathlib import Path
from typing import Optional, List, Dict, Any

import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.syntax import Syntax
from rich.prompt import Confirm, Prompt
import toml
import yaml

from .utils.configuration import IPsecEvaluatorConfigParser, ConfigurationError
from .utils.logging import setup_logging, get_logger
from .engine.orchestrator import EnhancedOrchestrator
from .engine.tester import EnhancedTester
from .engine.checker import <PERSON>hanced<PERSON>he<PERSON>, ComplianceStandard
from .models.base import TestMode, ComplianceLevel
from .models.scenario import TestScenario

# Initialize CLI app and console
app = typer.Typer(
    name="ipsec-evaluator",
    help="Enhanced IPsec compliance testing and evaluation tool",
    rich_markup_mode="rich"
)

# Configuration subcommand group
config_app = typer.Typer(
    name="config",
    help="Configuration management commands",
    rich_markup_mode="rich"
)
app.add_typer(config_app, name="config")

console = Console()
logger = get_logger(__name__)


@app.callback()
def main(
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose output"),
    log_level: str = typer.Option("INFO", "--log-level", help="Set logging level"),
    config_file: Optional[Path] = typer.Option(None, "--config", "-c", help="Configuration file path")
):
    """Enhanced IPsec compliance testing and evaluation tool."""
    # Setup logging
    setup_logging(verbose=verbose, level=log_level)

    # Store global options in app context
    ctx = typer.get_current_context()
    ctx.ensure_object(dict)
    ctx.obj['verbose'] = verbose
    ctx.obj['log_level'] = log_level
    ctx.obj['config_file'] = config_file


@config_app.command("init")
def config_init(
    template: str = typer.Option("basic", "--template", "-t", help="Configuration template type"),
    output: Path = typer.Option("ipsec-evaluator.toml", "--output", "-o", help="Output configuration file"),
    format_type: str = typer.Option("toml", "--format", "-f", help="Output format (toml, yaml)"),
    force: bool = typer.Option(False, "--force", help="Overwrite existing file"),
    interactive: bool = typer.Option(False, "--interactive", "-i", help="Interactive configuration")
):
    """Initialize a new configuration file from template."""

    # Check if output file exists
    if output.exists() and not force:
        if not Confirm.ask(f"Configuration file [bold]{output}[/bold] already exists. Overwrite?"):
            console.print("[yellow]Configuration initialization cancelled.[/yellow]")
            raise typer.Exit(1)

    try:
        # Get available templates
        available_templates = ['basic', 'anssi', 'performance', 'development']

        if template not in available_templates:
            console.print(f"[red]Error:[/red] Unknown template '{template}'")
            console.print(f"Available templates: {', '.join(available_templates)}")
            raise typer.Exit(1)

        # Create configuration from template
        template_data = IPsecEvaluatorConfigParser.create_template(template)

        # Interactive mode
        if interactive:
            template_data = _interactive_config_setup(template_data)

        # Save configuration
        if format_type.lower() == 'toml':
            with open(output, 'w') as f:
                toml.dump(template_data, f)
        elif format_type.lower() in {'yaml', 'yml'}:
            with open(output, 'w') as f:
                yaml.dump(template_data, f, default_flow_style=False)
        else:
            console.print(f"[red]Error:[/red] Unsupported format '{format_type}'")
            raise typer.Exit(1)

        console.print(f"[green]✓[/green] Configuration file created: [bold]{output}[/bold]")
        console.print(f"[blue]Template:[/blue] {template}")
        console.print(f"[blue]Format:[/blue] {format_type}")

        # Show next steps
        console.print("\n[bold]Next steps:[/bold]")
        console.print(f"1. Review and customize [bold]{output}[/bold]")
        console.print(f"2. Validate configuration: [bold]ipsec-evaluator config validate {output}[/bold]")
        console.print(f"3. Run tests: [bold]ipsec-evaluator test --config {output}[/bold]")

    except Exception as e:
        console.print(f"[red]Error creating configuration:[/red] {e}")
        raise typer.Exit(1)


@config_app.command("validate")
def config_validate(
    config_file: Path = typer.Argument(..., help="Configuration file to validate"),
    show_details: bool = typer.Option(False, "--details", "-d", help="Show detailed validation results")
):
    """Validate a configuration file."""

    if not config_file.exists():
        console.print(f"[red]Error:[/red] Configuration file not found: {config_file}")
        raise typer.Exit(1)

    try:
        # Load and validate configuration
        parser = IPsecEvaluatorConfigParser(
            config_files=[config_file],
            validate_config=True
        )

        console.print(f"[green]✓[/green] Configuration file [bold]{config_file}[/bold] is valid")

        if show_details:
            _show_config_details(parser)

    except ConfigurationError as e:
        console.print(f"[red]✗[/red] Configuration validation failed:")
        console.print(f"[red]{e}[/red]")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]Error:[/red] {e}")
        raise typer.Exit(1)


@config_app.command("show")
def config_show(
    config_file: Path = typer.Argument(..., help="Configuration file to display"),
    section: Optional[str] = typer.Option(None, "--section", "-s", help="Show specific section only"),
    format_output: str = typer.Option("table", "--format", "-f", help="Output format (table, json, yaml)")
):
    """Display configuration file contents."""

    if not config_file.exists():
        console.print(f"[red]Error:[/red] Configuration file not found: {config_file}")
        raise typer.Exit(1)

    try:
        parser = IPsecEvaluatorConfigParser(config_files=[config_file])

        if format_output == "table":
            _show_config_table(parser, section)
        elif format_output == "json":
            import json
            config_dict = parser.get_config_summary()
            console.print(json.dumps(config_dict, indent=2))
        elif format_output == "yaml":
            config_dict = parser.get_config_summary()
            console.print(yaml.dump(config_dict, default_flow_style=False))
        else:
            console.print(f"[red]Error:[/red] Unsupported format '{format_output}'")
            raise typer.Exit(1)

    except Exception as e:
        console.print(f"[red]Error:[/red] {e}")
        raise typer.Exit(1)


@config_app.command("templates")
def config_templates():
    """List available configuration templates."""

    templates = {
        'basic': 'Basic IPsec testing configuration',
        'anssi': 'ANSSI-compliant configuration with French security requirements',
        'performance': 'High-performance testing configuration with multiple algorithms',
        'development': 'Development-friendly configuration with local settings'
    }

    table = Table(title="Available Configuration Templates")
    table.add_column("Template", style="cyan", no_wrap=True)
    table.add_column("Description", style="white")

    for template_name, description in templates.items():
        table.add_row(template_name, description)

    console.print(table)

    console.print("\n[bold]Usage:[/bold]")
    console.print("ipsec-evaluator config init --template [template_name]")


@config_app.command("convert")
def config_convert(
    input_file: Path = typer.Argument(..., help="Input configuration file"),
    output_file: Path = typer.Argument(..., help="Output configuration file"),
    input_format: Optional[str] = typer.Option(None, "--input-format", help="Input format (auto-detect if not specified)"),
    output_format: Optional[str] = typer.Option(None, "--output-format", help="Output format (auto-detect if not specified)"),
    force: bool = typer.Option(False, "--force", help="Overwrite existing output file")
):
    """Convert configuration file between formats."""

    if not input_file.exists():
        console.print(f"[red]Error:[/red] Input file not found: {input_file}")
        raise typer.Exit(1)

    if output_file.exists() and not force:
        if not Confirm.ask(f"Output file [bold]{output_file}[/bold] already exists. Overwrite?"):
            console.print("[yellow]Conversion cancelled.[/yellow]")
            raise typer.Exit(1)

    try:
        # Load configuration
        parser = IPsecEvaluatorConfigParser(config_files=[input_file])

        # Determine output format
        if output_format is None:
            output_format = output_file.suffix.lstrip('.')

        # Save in new format
        parser.save_config(output_file, output_format)

        console.print(f"[green]✓[/green] Configuration converted:")
        console.print(f"[blue]From:[/blue] {input_file} ({input_file.suffix})")
        console.print(f"[blue]To:[/blue] {output_file} ({output_format})")

    except Exception as e:
        console.print(f"[red]Error:[/red] {e}")
        raise typer.Exit(1)



@app.command("test")
def test_run(
    config_file: Optional[Path] = typer.Option(None, "--config", "-c", help="Configuration file"),
    scenario: Optional[str] = typer.Option(None, "--scenario", "-s", help="Test scenario name"),
    mode: str = typer.Option("both", "--mode", "-m", help="Test mode (initiator, responder, both)"),
    output_dir: Optional[Path] = typer.Option(None, "--output", "-o", help="Output directory for results"),
    template: Optional[str] = typer.Option(None, "--template", "-t", help="Use configuration template"),
    dry_run: bool = typer.Option(False, "--dry-run", help="Show what would be executed without running"),
    compliance_check: bool = typer.Option(True, "--compliance/--no-compliance", help="Enable compliance checking")
):
    """Run IPsec compliance tests."""

    try:
        # Load configuration
        if config_file:
            if not config_file.exists():
                console.print(f"[red]Error:[/red] Configuration file not found: {config_file}")
                raise typer.Exit(1)
            parser = IPsecEvaluatorConfigParser(config_files=[config_file])
        elif template:
            parser = IPsecEvaluatorConfigParser.from_template(template)
        else:
            # Use default configuration
            parser = IPsecEvaluatorConfigParser.from_template('basic')

        config = parser.get_test_configuration()

        # Override output directory if specified
        if output_dir:
            config.global_config.results_dir = str(output_dir)

        if dry_run:
            _show_test_plan(config, scenario, mode, compliance_check)
            return

        # Create orchestrator
        orchestrator = EnhancedOrchestrator(config)

        console.print("[bold blue]🚀 Starting IPsec Compliance Tests[/bold blue]")
        console.print(f"[cyan]Configuration:[/cyan] {config_file or f'template:{template}' or 'default'}")
        console.print(f"[cyan]Mode:[/cyan] {mode}")
        console.print(f"[cyan]Output:[/cyan] {config.global_config.results_dir}")

        # Run tests (simplified for now)
        console.print("\n[yellow]⚠️  Test execution not yet implemented[/yellow]")
        console.print("This will be implemented with the full orchestrator integration.")

    except Exception as e:
        console.print(f"[red]Error:[/red] {e}")
        raise typer.Exit(1)


@app.command("validate")
def validate_compliance(
    results_dir: Path = typer.Argument(..., help="Results directory to validate"),
    standard: str = typer.Option("anssi", "--standard", "-s", help="Compliance standard (anssi, nist)"),
    output_format: str = typer.Option("table", "--format", "-f", help="Output format (table, json, yaml)")
):
    """Validate test results for compliance."""

    if not results_dir.exists():
        console.print(f"[red]Error:[/red] Results directory not found: {results_dir}")
        raise typer.Exit(1)

    console.print(f"[bold blue]🔍 Validating Compliance[/bold blue]")
    console.print(f"[cyan]Results:[/cyan] {results_dir}")
    console.print(f"[cyan]Standard:[/cyan] {standard}")

    # This would integrate with the checker
    console.print("\n[yellow]⚠️  Compliance validation not yet implemented[/yellow]")
    console.print("This will be implemented with the full checker integration.")


def _show_test_plan(config, scenario: Optional[str], mode: str, compliance_check: bool):
    """Show what would be executed in dry-run mode."""
    console.print("[bold blue]🔍 Test Execution Plan (Dry Run)[/bold blue]")

    # Configuration summary
    config_table = Table(title="Configuration Summary")
    config_table.add_column("Setting", style="cyan")
    config_table.add_column("Value", style="white")

    config_table.add_row("Max Concurrent Tests", str(config.global_config.max_concurrent_tests))
    config_table.add_row("Test Timeout", f"{config.global_config.test_timeout}s")
    config_table.add_row("Results Directory", config.global_config.results_dir)
    config_table.add_row("Initiator IP", config.network.initiator_ip)
    config_table.add_row("Responder IP", config.network.responder_ip)
    config_table.add_row("IKE Port", str(config.network.ike_port))

    console.print(config_table)

    # Test plan
    console.print("\n[bold]Test Execution Plan:[/bold]")
    console.print(f"• Mode: [cyan]{mode}[/cyan]")
    if scenario:
        console.print(f"• Scenario: [cyan]{scenario}[/cyan]")
    else:
        console.print("• Scenario: [cyan]All available scenarios[/cyan]")
    console.print(f"• Compliance Check: [cyan]{'Enabled' if compliance_check else 'Disabled'}[/cyan]")

    # Crypto configuration
    crypto_table = Table(title="Cryptographic Configuration")
    crypto_table.add_column("Algorithm Type", style="cyan")
    crypto_table.add_column("Algorithms", style="white")

    crypto_table.add_row("IKE Encryption", ", ".join(config.crypto.ike_encryption))
    crypto_table.add_row("IKE Integrity", ", ".join(config.crypto.ike_integrity))
    crypto_table.add_row("IKE PRF", ", ".join(config.crypto.ike_prf))
    crypto_table.add_row("IKE DH Groups", ", ".join(map(str, config.crypto.ike_dh_groups)))
    crypto_table.add_row("ESP Encryption", ", ".join(config.crypto.esp_encryption))
    crypto_table.add_row("ESP Integrity", ", ".join(config.crypto.esp_integrity))

    console.print(crypto_table)


if __name__ == "__main__":
    app()

"""
Test orchestrator for managing IPsec compliance testing.

The Orchestrator coordinates the entire testing process:
- Test scenario execution
- Result collection and reporting
- Resource management
"""

import asyncio
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
from uuid import uuid4

from ..models.base import TestStatus, TestMode
from ..models.config import TestConfiguration
from ..models.scenario import TestScenario
from ..models.results import TestResult, ComplianceReport
from ..utils.logging import get_logger
from .tester import Tester
from .checker import Checker

logger = get_logger(__name__)


class Orchestrator:
    """
    Main orchestrator for IPsec compliance testing.
    
    Manages the complete testing lifecycle including test execution, and result reporting.
    """
    
    def __init__(self, config: TestConfiguration):
        """
        Initialize the orchestrator.
        
        Args:
            config: Test configuration
        """
        self.config = config
        self.test_results: List[TestResult] = []
        self.active_tests: Dict[str, asyncio.Task] = {}
        self._semaphore = asyncio.Semaphore(config.global_config.max_concurrent_tests)
        
        logger.info("Orchestrator initialized")
    
    def execute_scenarios(
        self, 
        scenario_names: List[str], 
        mode: str = "both"
    ) -> List[TestResult]:
        """
        Execute test scenarios.
        
        Args:
            scenario_names: List of scenario names to execute
            mode: Test mode (initiator, responder, both)
            
        Returns:
            List of test results
        """
        logger.info(f"Starting execution of {len(scenario_names)} scenarios in {mode} mode")
        
        # Load scenarios
        scenarios = self._load_scenarios(scenario_names)
        
        # Execute tests
        results = asyncio.run(self._execute_scenarios_async(scenarios, mode))
        
        # Generate compliance report
        self._generate_compliance_report(results)
        
        return results
    
    async def _execute_scenarios_async(
        self, 
        scenarios: List[TestScenario], 
        mode: str
    ) -> List[TestResult]:
        """Execute scenarios asynchronously."""
        tasks = []
        
        for scenario in scenarios:
            if mode in ["initiator", "both"] and scenario.has_initiator_tests():
                task = self._create_test_task(scenario, TestMode.INITIATOR)
                tasks.append(task)
            
            if mode in ["responder", "both"] and scenario.has_responder_tests():
                task = self._create_test_task(scenario, TestMode.RESPONDER)
                tasks.append(task)
        
        # Execute all tests
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and log them
        valid_results = []
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Test execution failed: {result}")
            else:
                valid_results.append(result)
        
        return valid_results
    
    async def _create_test_task(
        self, 
        scenario: TestScenario, 
        mode: TestMode
    ) -> TestResult:
        """Create and execute a test task."""
        async with self._semaphore:
            return await self._execute_single_test(scenario, mode)
    
    async def _execute_single_test(
        self, 
        scenario: TestScenario, 
        mode: TestMode
    ) -> TestResult:
        """Execute a single test scenario."""
        test_id = str(uuid4())
        start_time = datetime.utcnow()
        
        logger.info(f"Starting test {test_id}: {scenario.name} ({mode})")
        
        try:
            # Create tester and checker
            tester = Tester(
                test_id=test_id,
                config=self.config,
                scenario=scenario,
                mode=mode
            )
            
            checker = Checker(
                test_id=test_id,
                config=self.config,
                scenario=scenario
            )
            
            # Execute test
            test_data = await tester.execute()
            
            # Check compliance
            compliance_result = await checker.check_compliance(test_data)
            
            # Create result
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            
            result = TestResult(
                test_id=test_id,
                scenario_name=scenario.name,
                mode=mode.value,
                status=TestStatus.COMPLETED,
                compliance=compliance_result.level,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                test_data=test_data,
                compliance_details=compliance_result
            )
            
            logger.info(f"Test {test_id} completed: {compliance_result.level}")
            return result
            
        except Exception as e:
            logger.error(f"Test {test_id} failed: {e}")
            
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            
            return TestResult(
                test_id=test_id,
                scenario_name=scenario.name,
                mode=mode.value,
                status=TestStatus.FAILED,
                compliance="unknown",
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                error_message=str(e)
            )
    
    def _load_scenarios(self, scenario_names: List[str]) -> List[TestScenario]:
        """Load test scenarios from files."""
        scenarios = []
        scenarios_dir = Path("scenarios")
        
        for name in scenario_names:
            scenario_file = scenarios_dir / f"{name}.yml"
            if not scenario_file.exists():
                scenario_file = scenarios_dir / f"{name}.yaml"
            
            if not scenario_file.exists():
                logger.error(f"Scenario file not found: {name}")
                continue
            
            try:
                scenario = TestScenario.from_file(scenario_file)
                scenarios.append(scenario)
                logger.info(f"Loaded scenario: {scenario.name}")
            except Exception as e:
                logger.error(f"Failed to load scenario {name}: {e}")
        
        return scenarios
    
    def _generate_compliance_report(self, results: List[TestResult]) -> None:
        """Generate and save compliance report."""
        report = ComplianceReport(
            generated_at=datetime.utcnow(),
            total_tests=len(results),
            passed_tests=len([r for r in results if r.compliance == "compliant"]),
            failed_tests=len([r for r in results if r.compliance == "non_compliant"]),
            results=results
        )
        
        # Save report
        report_file = self.config.results_dir / f"compliance_report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
        report.save_to_file(report_file)
        
        logger.info(f"Compliance report saved: {report_file}")
        logger.info(f"Test summary: {report.passed_tests}/{report.total_tests} passed")

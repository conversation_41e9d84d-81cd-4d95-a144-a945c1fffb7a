"""
Enhanced IKEv2 payload builder with hook support.

This module provides a modern, extensible payload building system that
improves upon the original ipsecdr implementation with better structure,
hook integration, and ANSSI compliance validation.
"""

import secrets
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

from scapy.contrib.ikev2 import (
    IKEv2, IKEv2_SA, IKEv2_Proposal, IKEv2_Transform,
    IKEv2_KE, IKEv2_Nonce, IKEv2_Notify, IKEv2_IDi, IKEv2_IDr,
    IKEv2_AUTH, IKEv2_CERT, IKEv2_CERTREQ, IKEv2_TSi, IKEv2_TSr,
    IKEv2_Encrypted, IPv4TrafficSelector
)

from ...utils.logging import get_logger
from ..crypto.algorithms import EncryptionAlgorithm, IntegrityAlgorithm, PRFAlgorithm

logger = get_logger(__name__)


class PayloadType(Enum):
    """IKEv2 payload types."""
    SA = "SA"
    KE = "KE"
    NONCE = "Nonce"
    NOTIFY = "Notify"
    IDi = "IDi"
    IDr = "IDr"
    AUTH = "AUTH"
    CERT = "CERT"
    CERTREQ = "CERTREQ"
    TSi = "TSi"
    TSr = "TSr"
    ENCRYPTED = "Encrypted"


class TransformType(Enum):
    """IKEv2 transform types."""
    ENCR = 1  # Encryption Algorithm
    PRF = 2   # Pseudo-random Function
    INTEG = 3 # Integrity Algorithm
    DH = 4    # Diffie-Hellman Group
    ESN = 5   # Extended Sequence Numbers


@dataclass
class ProposalConfig:
    """Configuration for an IKEv2 proposal."""

    proposal_num: int
    protocol_id: int
    spi: Optional[bytes] = None

    # Transform configurations
    encryption_algorithms: List[int] = None
    encryption_key_lengths: List[int] = None
    integrity_algorithms: List[int] = None
    prf_algorithms: List[int] = None
    dh_groups: List[int] = None
    esn_values: List[int] = None

    def __post_init__(self):
        """Set default values if not provided."""
        if self.encryption_algorithms is None:
            self.encryption_algorithms = [20]  # AES-GCM-16
        if self.encryption_key_lengths is None:
            self.encryption_key_lengths = [256]
        if self.integrity_algorithms is None:
            self.integrity_algorithms = []  # Not needed for AEAD
        if self.prf_algorithms is None:
            self.prf_algorithms = [5]  # PRF_HMAC_SHA2_256
        if self.dh_groups is None:
            self.dh_groups = [19]  # 256-bit random ECP group
        if self.esn_values is None:
            self.esn_values = [1]  # ESN enabled


class IKEv2PayloadBuilder:
    """
    Enhanced IKEv2 payload builder with hook support and ANSSI compliance.

    This class provides methods to build various IKEv2 payloads with:
    - ANSSI compliance validation
    - Hook integration for custom payload modification
    - Enhanced error handling and logging
    - Support for modern cryptographic algorithms
    """

    # ANSSI-approved algorithms
    ANSSI_ENCRYPTION = {
        13: "ENCR_AES_CTR",      # AES-CTR
        20: "ENCR_AES_GCM_16"   # AES-GCM-16
    }

    ANSSI_INTEGRITY = {
        12: "AUTH_HMAC_SHA2_256_128"  # HMAC-SHA2-256
    }

    ANSSI_PRF = {
        5: "PRF_HMAC_SHA2_256"  # PRF_HMAC_SHA2_256
    }

    ANSSI_DH_GROUPS = {
        19: "secp256r1 (256-bit random ECP group)",
        20: "secp384r1 (384-bit random ECP group)",
        21: "secp521r1 (521-bit random ECP group)",
        27: "brainpoolP224r1 (224-bit Brainpool ECP group)",
        28: "brainpoolP256r1 (256-bit Brainpool ECP group)",
        29: "brainpoolP384r1 (384-bit Brainpool ECP group)",
        30: "brainpoolP512r1 (512-bit Brainpool ECP group)"
    }

    def __init__(self, validate_anssi: bool = True):
        """
        Initialize the payload builder.

        Args:
            validate_anssi: Whether to validate ANSSI compliance
        """
        self.validate_anssi = validate_anssi
        logger.debug(f"IKEv2PayloadBuilder initialized (ANSSI validation: {validate_anssi})")

    def build_sa_payload(
        self,
        proposals: List[ProposalConfig],
        next_payload: str = "KE"
    ) -> IKEv2_SA:
        """
        Build a Security Association payload.

        Args:
            proposals: List of proposal configurations
            next_payload: Next payload type

        Returns:
            IKEv2_SA payload
        """
        logger.debug(f"Building SA payload with {len(proposals)} proposals")

        sa_payload = IKEv2_SA(next_payload=next_payload)

        for prop_config in proposals:
            # Validate ANSSI compliance if enabled
            if self.validate_anssi:
                self._validate_proposal_anssi_compliance(prop_config)

            proposal = self._build_proposal(prop_config)
            sa_payload = sa_payload / proposal

        return sa_payload

    def _build_proposal(self, config: ProposalConfig) -> IKEv2_Proposal:
        """Build a single proposal."""

        proposal = IKEv2_Proposal(
            proposal_num=config.proposal_num,
            protocol_id=config.protocol_id,
            spi_size=len(config.spi) if config.spi else 0,
            spi=config.spi or b"",
            trans_nb=self._count_transforms(config)
        )

        # Add encryption transforms
        for i, encr_alg in enumerate(config.encryption_algorithms):
            transform = IKEv2_Transform(
                transform_type=TransformType.ENCR.value,
                transform_id=encr_alg
            )

            # Add key length attribute if specified
            if config.encryption_key_lengths and i < len(config.encryption_key_lengths):
                key_length = config.encryption_key_lengths[i]
                # Add key length attribute (type 14)
                transform.attributes = [(14, key_length)]

            proposal = proposal / transform

        # Add PRF transforms
        for prf_alg in config.prf_algorithms:
            transform = IKEv2_Transform(
                transform_type=TransformType.PRF.value,
                transform_id=prf_alg
            )
            proposal = proposal / transform

        # Add integrity transforms (if not AEAD)
        for integ_alg in config.integrity_algorithms:
            transform = IKEv2_Transform(
                transform_type=TransformType.INTEG.value,
                transform_id=integ_alg
            )
            proposal = proposal / transform

        # Add DH group transforms
        for dh_group in config.dh_groups:
            transform = IKEv2_Transform(
                transform_type=TransformType.DH.value,
                transform_id=dh_group
            )
            proposal = proposal / transform

        # Add ESN transforms
        for esn_val in config.esn_values:
            transform = IKEv2_Transform(
                transform_type=TransformType.ESN.value,
                transform_id=esn_val
            )
            proposal = proposal / transform

        return proposal

    def _count_transforms(self, config: ProposalConfig) -> int:
        """Count the total number of transforms in a proposal."""
        count = 0
        count += len(config.encryption_algorithms)
        count += len(config.prf_algorithms)
        count += len(config.integrity_algorithms)
        count += len(config.dh_groups)
        count += len(config.esn_values)
        return count

    def build_ke_payload(
        self,
        dh_group: int,
        public_key: bytes,
        next_payload: str = "Nonce"
    ) -> IKEv2_KE:
        """
        Build a Key Exchange payload.

        Args:
            dh_group: Diffie-Hellman group number
            public_key: Public key data
            next_payload: Next payload type

        Returns:
            IKEv2_KE payload
        """
        logger.debug(f"Building KE payload for DH group {dh_group}")

        # Validate ANSSI compliance
        if self.validate_anssi and dh_group not in self.ANSSI_DH_GROUPS:
            logger.warning(f"DH group {dh_group} is not ANSSI-approved")

        return IKEv2_KE(
            next_payload=next_payload,
            group=dh_group,
            kex_data=public_key
        )

    def build_nonce_payload(
        self,
        nonce_data: Optional[bytes] = None,
        nonce_size: int = 32,
        next_payload: str = "None"
    ) -> IKEv2_Nonce:
        """
        Build a Nonce payload.

        Args:
            nonce_data: Nonce data (generated if None)
            nonce_size: Size of nonce in bytes
            next_payload: Next payload type

        Returns:
            IKEv2_Nonce payload
        """
        if nonce_data is None:
            nonce_data = secrets.token_bytes(nonce_size)

        logger.debug(f"Building Nonce payload ({len(nonce_data)} bytes)")

        return IKEv2_Nonce(
            next_payload=next_payload,
            nonce=nonce_data
        )

    def build_notify_payload(
        self,
        notify_type: int,
        notify_data: bytes = b"",
        spi: bytes = b"",
        next_payload: str = "None"
    ) -> IKEv2_Notify:
        """
        Build a Notify payload.

        Args:
            notify_type: Notification type
            notify_data: Notification data
            spi: Security Parameter Index
            next_payload: Next payload type

        Returns:
            IKEv2_Notify payload
        """
        logger.debug(f"Building Notify payload (type: {notify_type})")

        return IKEv2_Notify(
            next_payload=next_payload,
            notify_type=notify_type,
            spi_size=len(spi),
            spi=spi,
            notify_data=notify_data
        )

    def build_id_payload(
        self,
        id_type: int,
        id_data: bytes,
        is_initiator: bool = True,
        next_payload: str = "None"
    ) -> Union[IKEv2_IDi, IKEv2_IDr]:
        """
        Build an ID payload.

        Args:
            id_type: ID type
            id_data: ID data
            is_initiator: Whether this is initiator ID
            next_payload: Next payload type

        Returns:
            IKEv2_IDi or IKEv2_IDr payload
        """
        logger.debug(f"Building ID payload (type: {id_type}, initiator: {is_initiator})")

        if is_initiator:
            return IKEv2_IDi(
                next_payload=next_payload,
                id_type=id_type,
                id_data=id_data
            )
        else:
            return IKEv2_IDr(
                next_payload=next_payload,
                id_type=id_type,
                id_data=id_data
            )

    def build_auth_payload(
        self,
        auth_method: int,
        auth_data: bytes,
        next_payload: str = "None"
    ) -> IKEv2_AUTH:
        """
        Build an AUTH payload.

        Args:
            auth_method: Authentication method
            auth_data: Authentication data
            next_payload: Next payload type

        Returns:
            IKEv2_AUTH payload
        """
        logger.debug(f"Building AUTH payload (method: {auth_method})")

        return IKEv2_AUTH(
            next_payload=next_payload,
            auth_method=auth_method,
            auth_data=auth_data
        )

    def build_ts_payload(
        self,
        traffic_selectors: List[Dict],
        is_initiator: bool = True,
        next_payload: str = "None"
    ) -> Union[IKEv2_TSi, IKEv2_TSr]:
        """
        Build a Traffic Selector payload.

        Args:
            traffic_selectors: List of traffic selector configurations
            is_initiator: Whether this is initiator TS
            next_payload: Next payload type

        Returns:
            IKEv2_TSi or IKEv2_TSr payload
        """
        logger.debug(f"Building TS payload ({len(traffic_selectors)} selectors, initiator: {is_initiator})")

        # Build traffic selector objects
        ts_objects = []
        for ts_config in traffic_selectors:
            ts = IPv4TrafficSelector(
                ip_protocol_id=ts_config.get("protocol", 0),
                start_port=ts_config.get("start_port", 0),
                end_port=ts_config.get("end_port", 65535),
                start_addr=ts_config.get("start_addr", "0.0.0.0"),
                end_addr=ts_config.get("end_addr", "***************")
            )
            ts_objects.append(ts)

        if is_initiator:
            payload = IKEv2_TSi(next_payload=next_payload, nb_ts=len(ts_objects))
        else:
            payload = IKEv2_TSr(next_payload=next_payload, nb_ts=len(ts_objects))

        # Add traffic selectors
        for ts in ts_objects:
            payload = payload / ts

        return payload

    def _validate_proposal_anssi_compliance(self, config: ProposalConfig):
        """Validate proposal against ANSSI requirements."""

        # Check encryption algorithms
        for encr_alg in config.encryption_algorithms:
            if encr_alg not in self.ANSSI_ENCRYPTION:
                logger.warning(f"Encryption algorithm {encr_alg} is not ANSSI-approved")

        # Check integrity algorithms (if any)
        for integ_alg in config.integrity_algorithms:
            if integ_alg not in self.ANSSI_INTEGRITY:
                logger.warning(f"Integrity algorithm {integ_alg} is not ANSSI-approved")

        # Check PRF algorithms
        for prf_alg in config.prf_algorithms:
            if prf_alg not in self.ANSSI_PRF:
                logger.warning(f"PRF algorithm {prf_alg} is not ANSSI-approved")

        # Check DH groups
        for dh_group in config.dh_groups:
            if dh_group not in self.ANSSI_DH_GROUPS:
                logger.warning(f"DH group {dh_group} is not ANSSI-approved")

        # Check key lengths
        for key_length in config.encryption_key_lengths or []:
            if key_length < 256:
                logger.warning(f"Key length {key_length} bits is below ANSSI minimum (256 bits)")

    def get_anssi_compliant_proposal(self, prefer_brainpool: bool = False) -> ProposalConfig:
        """
        Get a default ANSSI-compliant proposal configuration.

        Args:
            prefer_brainpool: Whether to prefer Brainpool curves over secp curves

        Returns:
            ANSSI-compliant proposal configuration
        """
        # Choose DH group based on preference
        if prefer_brainpool:
            dh_group = 28  # brainpoolP256r1
        else:
            dh_group = 19  # secp256r1

        return ProposalConfig(
            proposal_num=1,
            protocol_id=1,  # IKE
            encryption_algorithms=[20],  # AES-GCM-16
            encryption_key_lengths=[256],
            integrity_algorithms=[],  # Not needed for AEAD
            prf_algorithms=[5],  # PRF_HMAC_SHA2_256
            dh_groups=[dh_group],
            esn_values=[1]  # ESN enabled
        )

    def get_enhanced_anssi_proposals(self) -> List[ProposalConfig]:
        """
        Get multiple ANSSI-compliant proposals with different curve types.

        Returns:
            List of ANSSI-compliant proposals covering secp and brainpool curves
        """
        proposals = []

        # Proposal 1: secp256r1 with AES-GCM-16
        proposals.append(ProposalConfig(
            proposal_num=1,
            protocol_id=1,
            encryption_algorithms=[20],  # AES-GCM-16
            encryption_key_lengths=[256],
            integrity_algorithms=[],
            prf_algorithms=[5],  # PRF_HMAC_SHA2_256
            dh_groups=[19],  # secp256r1
            esn_values=[1]
        ))

        # Proposal 2: brainpoolP256r1 with AES-GCM-16
        proposals.append(ProposalConfig(
            proposal_num=2,
            protocol_id=1,
            encryption_algorithms=[20],  # AES-GCM-16
            encryption_key_lengths=[256],
            integrity_algorithms=[],
            prf_algorithms=[5],  # PRF_HMAC_SHA2_256
            dh_groups=[28],  # brainpoolP256r1
            esn_values=[1]
        ))

        # Proposal 3: secp384r1 with AES-GCM-16 (higher security)
        proposals.append(ProposalConfig(
            proposal_num=3,
            protocol_id=1,
            encryption_algorithms=[20],  # AES-GCM-16
            encryption_key_lengths=[256],
            integrity_algorithms=[],
            prf_algorithms=[5],  # PRF_HMAC_SHA2_256
            dh_groups=[20],  # secp384r1
            esn_values=[1]
        ))

        # Proposal 4: brainpoolP384r1 with AES-GCM-16 (higher security)
        proposals.append(ProposalConfig(
            proposal_num=4,
            protocol_id=1,
            encryption_algorithms=[20],  # AES-GCM-16
            encryption_key_lengths=[256],
            integrity_algorithms=[],
            prf_algorithms=[5],  # PRF_HMAC_SHA2_256
            dh_groups=[29],  # brainpoolP384r1
            esn_values=[1]
        ))

        return proposals

    def get_curve_type_for_dh_group(self, dh_group: int) -> Optional[str]:
        """
        Get the curve type for a DH group.

        Args:
            dh_group: DH group number

        Returns:
            Curve type string or None if not a curve
        """
        if dh_group in [19, 20, 21, 25, 26]:
            return "secp"
        elif dh_group in [27, 28, 29, 30]:
            return "brainpool"
        elif dh_group == 31:
            return "curve25519"
        elif dh_group == 32:
            return "curve448"
        else:
            return None

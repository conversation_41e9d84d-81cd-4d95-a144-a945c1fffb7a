"""
Configuration models for IPsec Evaluator.
"""

from pathlib import Path
from typing import Dict, List, Optional, Union

from pydantic import Field, validator

from .base import ConfigModel


class GlobalConfig(ConfigModel):
    """Global application configuration."""
    
    timeout: int = Field(30, description="Default timeout in seconds", ge=1)
    verbose: bool = Field(False, description="Enable verbose logging")
    log_level: str = Field("INFO", description="Logging level")
    log_file: Optional[Path] = Field(None, description="Log file path")
    max_concurrent_tests: int = Field(5, description="Maximum concurrent tests", ge=1)
    
    @validator("log_level")
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of {valid_levels}")
        return v.upper()


class NetworkConfig(ConfigModel):
    """Network configuration for IPsec testing."""
    
    # Interface configuration
    interface: str = Field("eth0", description="Network interface")
    
    # IP addresses
    initiator_ip: str = Field("************", description="Initiator IP address")
    responder_ip: str = Field("************", description="Responder IP address")
    
    # Ports
    ike_port: int = Field(500, description="IKE port", ge=1, le=65535)
    nat_t_port: int = Field(4500, description="NAT-T port", ge=1, le=65535)
    
    # NAT traversal
    nat_traversal: bool = Field(True, description="Enable NAT traversal")
    
    # Network ranges
    test_network: str = Field("*************/24", description="Test network CIDR")
    
    @validator("initiator_ip", "responder_ip")
    def validate_ip(cls, v):
        import ipaddress
        try:
            ipaddress.ip_address(v)
        except ValueError:
            raise ValueError(f"Invalid IP address: {v}")
        return v


class CryptoConfig(ConfigModel):
    """Cryptographic configuration."""
    
    # IKE SA algorithms
    ike_encryption: List[str] = Field(
        ["AES_GCM_16"], 
        description="IKE encryption algorithms"
    )
    ike_integrity: List[str] = Field(
        ["HMAC_SHA2_256"], 
        description="IKE integrity algorithms"
    )
    ike_prf: List[str] = Field(
        ["PRF_HMAC_SHA2_256"], 
        description="IKE PRF algorithms"
    )
    ike_dh_groups: List[int] = Field(
        [19], 
        description="IKE DH groups"
    )
    
    # Child SA algorithms
    esp_encryption: List[str] = Field(
        ["AES_GCM_16"], 
        description="ESP encryption algorithms"
    )
    esp_integrity: List[str] = Field(
        ["HMAC_SHA2_256"], 
        description="ESP integrity algorithms"
    )
    
    # Key sizes
    encryption_key_sizes: List[int] = Field(
        [256], 
        description="Encryption key sizes in bits"
    )
    
    # Certificate configuration
    ca_cert_path: Optional[Path] = Field(None, description="CA certificate path")
    cert_path: Optional[Path] = Field(None, description="Entity certificate path")
    key_path: Optional[Path] = Field(None, description="Private key path")


class TestConfiguration(ConfigModel):
    """Complete test configuration combining all config sections."""
    
    global_config: GlobalConfig = Field(default_factory=GlobalConfig)
    network: NetworkConfig = Field(default_factory=NetworkConfig)
    crypto: CryptoConfig = Field(default_factory=CryptoConfig)
    
    # Additional test-specific configuration
    test_data_dir: Path = Field(Path("./test_data"), description="Test data directory")
    results_dir: Path = Field(Path("./results"), description="Results directory")
    
    @validator("test_data_dir", "results_dir")
    def ensure_path_exists(cls, v):
        if isinstance(v, str):
            v = Path(v)
        v.mkdir(parents=True, exist_ok=True)
        return v
